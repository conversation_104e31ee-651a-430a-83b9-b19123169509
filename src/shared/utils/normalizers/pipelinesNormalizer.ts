import type {
  JobParticipationModel,
  NormalizedJobParticipationModel,
} from '@shared/types/jobsProps';
import type { JobPiplineData } from '@shared/types/pipelineProps';
import handleFullname from '../handleFullname';

const sortPipelinesNormalizer = (orgData: JobPiplineData) => {
  const sortedPipelines = orgData.pipelines.sort((a, b) => a.order - b.order);

  return {
    ...orgData,
    pipelines: sortedPipelines,
  };
};

const pipelinesNormalizer = (
  orgData: JobParticipationModel[]
): NormalizedJobParticipationModel[] => {
  if (!orgData.length) return [];

  return orgData.reduce((acc, curr) => {
    if (curr.type === 'CANDIDATE') {
      return [
        ...acc,
        {
          ...curr,
          type: '',

          rejected: true,

          user: {
            id: curr.id,
            croppedImageUrl: curr.candidate.profile.croppedImageUrl,
            fullName: curr.candidate.profile.fullName,
            occupationName: curr.candidate.profile.occupationName,
            username: curr.candidate.profile.username,
            usernameAtSign: `@${curr.candidate.profile.username}`,
            location: curr.candidate.profile?.location,

            rejected: true,
          },
        },
      ];
    }

    return [
      ...acc,
      {
        ...curr,
        type: '',
        rejected: true,

        user: {
          id: curr.id,
          croppedImageUrl: curr.applicant.croppedImageUrl,
          fullName: handleFullname(curr.applicant),
          occupationName: curr.applicant.occupationName,
          username: curr.applicant.username,
          usernameAtSign: `@${curr.applicant.username}`,
          location: curr.applicant.location?.title,
          rejected: true,
        },
      },
    ];
  }, [] as any[]);
};

export { sortPipelinesNormalizer, pipelinesNormalizer };
