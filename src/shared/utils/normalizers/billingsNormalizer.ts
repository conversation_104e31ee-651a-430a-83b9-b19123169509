import { StyledLogo } from '@shared/svg/LogoIcon';
import type { BillingCardProps } from '@shared/components/Organism/Billing/types';
import type { BEJobBilling } from '@shared/types/job';
import type { PlanTimeSpan } from '@shared/types/page';

export const jobBillingNormalizer = ({
  amount,
  invoiceModel,
  jobModel,
}: BEJobBilling): BillingCardProps['data'] => ({
  ...invoiceModel,
  billingType: 'PUBLISH_JOB',
  Logo: StyledLogo,
  planName: jobModel?.title || '',
  timeSpan: (jobModel?.pageTitle || '') as PlanTimeSpan,
  priceUnit: 'per_job',
  price: amount,
});
