import { type PlanTimeSpan } from '@shared/types/page';
import useReactQuery from './useReactQuery';
import { QueryKeys } from '../constants';
import { type AppPortal } from '../getAppEnv';
import { getPlansList } from '../api/page';

export function useGetPlansData({
  appPortal,
  timeSpan = 'MONTHLY',
}: {
  appPortal: AppPortal;
  timeSpan?: PlanTimeSpan;
}) {
  return useReactQuery({
    action: {
      key: [QueryKeys.getPlansList, appPortal, timeSpan],
      apiFunc: getPlansList,
      params: {
        portal: appPortal,
        timeSpan,
      },
      spreadParams: true,
    },
    config: {
      cacheTime: 2 * 60 * 60 * 1000, // 2 hours
      staleTime: 60 * 60 * 1000, // 1 hour
      enabled: appPortal && appPortal !== 'user',
    },
  });
}
