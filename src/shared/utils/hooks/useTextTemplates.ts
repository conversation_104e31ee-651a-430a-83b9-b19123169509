import { useMemo, useState } from 'react';
import type { BETemplateResponse, TemplateCategoryType } from '../api/template';
import { searchAllTemplates } from '../api/template';
import useReactInfiniteQuery from './useInfiniteQuery';
import { QueryKeys } from '../constants';
import useReactMutation from './useReactMutation';
import templateEndPoints from '../constants/servicesEndpoints/services/template';

export default function useTextTemplates(
  category: TemplateCategoryType,
  id?: string
) {
  const [searchKeyword, setSearchKeyword] = useState('');
  const allTemplatesQuery = useReactInfiniteQuery<BETemplateResponse>(
    [QueryKeys.searchAllTemplates, category, searchKeyword],
    {
      func: searchAllTemplates,
      extraProps: {
        category,
        text: searchKeyword?.length ? searchKeyword : undefined,
      },
    }
  );

  const selectedTemplate = useMemo(
    () => allTemplatesQuery.data?.find((item) => item.id === id),
    [allTemplatesQuery, id]
  );

  const { mutate: createTemplate } = useReactMutation({
    url: templateEndPoints.byId(category),
    method: 'POST',
  });
  const { mutate: editTemplate } = useReactMutation({
    url: templateEndPoints.byId(category, id),
    method: 'PUT',
  });
  const { mutate: deleteTemplate } = useReactMutation({
    url: templateEndPoints.byId(category),
    method: 'DELETE',
  });

  return {
    ...allTemplatesQuery,
    list: allTemplatesQuery.data,
    data: selectedTemplate,
    setSearchKeyword,
    createTemplate,
    editTemplate,
    deleteTemplate,
  };
}
