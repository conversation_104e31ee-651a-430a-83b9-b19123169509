import type { jobSelectedKeys, jobStatusKeys } from 'shared/utils/constants';
import type { IJobLanguage } from './language';
import type {
  BeforeCachePageDetailType,
  IPage,
  PageApiResponseType,
} from './page';
import type { OwnerProfileInfo } from './Owner';
import type { ILocation } from './lookup';
import type { PeopleType } from './people';
import type { JobDetailsCollaboratorProps } from './jobsProps';
import type { Billing } from '@shared/components/Organism/Billing/types';

export enum ISkillLevelEnum {
  BEGINNER = 'BEGINNER',
  INTERMEDIATE = 'INTERMEDIATE',
  ADVANCED = 'ADVANCED',
  EXPERT = 'EXPERT',
}

export interface ISkill {
  skillId?: number;
  skillType?: string;
  skillLevel: ISkillLevelEnum;
  skillName?: string;
}

export interface ILanguage {
  languageId: number;
  languageLevel: string;
  languageName?: string;
}

export interface IWorkStatus {
  workStatus: string;
}

export interface IQuestionChoice {
  answer: string;
}

export interface IOwner {
  type: 'CREATOR' | 'OWNER' | 'COLLABORATOR';
  userId: string;
}

export interface IJobWorkPlaceType {
  label: string;
  value: string;
}

export interface IQuestion {
  question: string;
  questionType: string;
  isRequired: boolean;
  isMultiSelect?: boolean;
  questionChoices: IQuestionChoice[];
}

export interface IJob {
  id: string;
  languageId: string;
  isAddExternalJobLink: boolean;
  pageCroppedImageUrl?: string;
  websiteUrl?: any;
  title?: {
    label: string | undefined;
    value: string | undefined;
  };
  workPlaceType: {
    label: string;
    value: string;
    icon: string;
    tooltip: string;
  };
  category?: {
    label: string | undefined;
    value: string | undefined;
  };
  employmentType?: {
    label: string | undefined;
    value: string | undefined;
  };
  description: string;
  experience?: {
    label: string | undefined;
    value: string | undefined;
  };
  skills: Skill[];
  languages: IJobLanguage[];
  salaryCurrency?: any;
  salaryRangeMin?: any;
  salaryRangeMax?: any;
  salaryPeriod?: any;
  workStatus?: any;
  questions?: any;
  alertEnable: boolean;
  alertFrequency: string;
  alertEmailEnable: boolean;
  alertPushEnable: boolean;
  status: {
    label: string;
    value: string;
  };
  createdDate: Date;
  updatedDate: Date;
  ownerId: string;
  owner?: {
    title: string;
    id: string;
    image: string;
    usernameAtSign?: string;
  };
  owners?: Array<{
    id: string;
    userId: string;
    user: OwnerProfileInfo;
  }>;
  createdUserId: string;
  associateUserId?: string;
  updatedUserId: string;
  isApplied: boolean;
  isHired?: boolean;
  isSaved: boolean;
  applicationId?: string;
  applicantsCount?: string;
  jobSaveId?: string;
  location?: ILocation;
  creatorUserId?: string;
  page: BeforeCachePageDetailType;
  isNew?: boolean;
  isClosed?: boolean;
  isExternal?: boolean;
  viewCount?: string;
  applicants?: Array<PeopleType>;
  tags?: string[];
  priority?: string;
  resumeRequired?: boolean;
  emailRequired?: boolean;
  phoneRequired?: boolean;
  coverLetterRequired?: boolean;
}

export type JobStatusType = keyof typeof jobStatusKeys;
export type JobSelectedType = keyof typeof jobSelectedKeys;

export interface IJobApi {
  id: string;
  languageId: string;
  isAddExternalJobLink: boolean;
  websiteUrl?: string;
  title: string;
  titleId?: string;
  workPlaceType: string;
  location: any;
  categoryId: string;
  categoryName: string;
  employmentType: string;
  description: string;
  experienceLevel?: any;
  salaryCurrencyId?: any;
  salaryCurrencySymbol?: string;
  salaryCurrencyName?: string;
  salaryCurrencyCode?: string;
  salaryRangeMin?: any;
  salaryRangeMax?: any;
  salaryPeriod?: any;
  workStatus?: any;
  skills: ISkill[] | string[];
  languages: ILanguage[];
  questions?: any;
  alertEnable: boolean;
  alertFrequency: string;
  alertEmailEnable: boolean;
  alertPushEnable: boolean;
  status: string;
  createdDate: Date;
  updatedDate: Date;
  ownerId: string;
  createdUserId: string;
  updatedUserId: string;
  isApplied: boolean;
  isHired?: boolean;
  isSaved: boolean;
  jobApplicationId?: string;
  applicantsCount?: string;
  jobSaveId?: string;
  creatorUserId?: string;
  page: PageApiResponseType;
  isNew?: boolean;
  applicants?: Array<any>;
  resumeRequired?: boolean;
  emailRequired?: boolean;
  phoneRequired?: boolean;
  coverLetterRequired?: boolean;
  pointOfContact: JobDetailsCollaboratorProps;
}

export interface ICreateJobData {
  languageId: number;
  isReceiveApplicantsByLobox: boolean;
  websiteUrl?: any;
  title: string;
  description: string;
  titleId: number;
  categoryId: number;
  employmentType: string;
  isRemote: boolean;
  experienceLevel: string;
  isWorkStatusesRequired: boolean;
  salaryCurrencyId: number;
  salaryRangeMin: number;
  salaryRangeMax: number;
  salaryPeriod: string;
  location: ILocation;
  skills: ISkill[];
  languages: ILanguage[];
  workStatuses: IWorkStatus[];
  questions: IQuestion[];
  owners: IOwner[];
}

export interface IAppliedJobData {
  jobId: string;
  email: string;
  coverLetter?: string;
  phone?: string;
  resume?: IResume;
  resumeFileId?: string;
  questions: IQuestion[];
  followPage?: boolean;
}

export interface IResume {
  createdDate: string;
  downloadPath: string;
  fileType: string;
  id: string;
  name: string;
  userId: string;
  link?: string;
  url?: string;
  originalFile?: File;
}

export type CompanyInfoType = BeforeCachePageDetailType & {
  mutuals: Array<IPage | PeopleType>;
  worksHere: Array<PeopleType>;
  location: ILocation;
  worksHereCount: number;
  mutualConnectionsCount: number;
};

export type BEJobBilling = {
  amount: string;
  invoiceModel?: Billing;
  jobModel?: IJobApi;
};
