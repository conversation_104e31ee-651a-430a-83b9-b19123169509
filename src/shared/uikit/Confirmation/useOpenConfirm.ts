import { useContext } from 'react';
import type { IConfirmContext } from './confirm.provider';
import { useConfirmContext } from './confirm.provider';
import type { UseConfirmProps } from './useConfirm';
import type { ConfirmationProps } from './Confirmation';

type Config = {
  variant?: ConfirmationProps['variant'];
  styles?: UseConfirmProps['styles'];
  isNarrow?: boolean;
};

const useOpenConfirm = ({
  variant,
  styles,
  isNarrow,
}: Config = {}): IConfirmContext => {
  const { openConfirmDialog: open, closeConfirm } = useConfirmContext();

  const openConfirmDialog = (props: UseConfirmProps) => {
    open({ ...props, variant, isNarrow, styles });
  };

  return { openConfirmDialog, closeConfirm };
};

export default useOpenConfirm;
