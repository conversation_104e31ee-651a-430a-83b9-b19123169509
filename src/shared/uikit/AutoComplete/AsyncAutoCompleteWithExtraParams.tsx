import type { AsyncAutoCompleteProps } from 'shared/types/components/Form.type';
import useGetAppObject from '../../hooks/useGetAppObject';
import AsyncAutoComplete from './AsyncAutoComplete';

const AsyncAutoCompleteWithExtraParams = (props: AsyncAutoCompleteProps) => {
  const { authUser } = useGetAppObject();
  const countryCode = authUser?.location?.countryCode;
  const defaultParams = { countryCode };

  return (
    <AsyncAutoComplete
      {...props}
      params={{ ...defaultParams, ...props.params }}
    />
  );
};

export default AsyncAutoCompleteWithExtraParams;
