import { useProfileModals } from '@shared/contexts/profileModals.provider';
import useProfilePage from 'shared/hooks/useProfilePage';
import { PAGE_ROLES } from 'shared/utils/constants/enums';

const useVisibleProfileSectionActionButton = (): boolean => {
  const { isAuthUser, hasMembership, roles } = useProfilePage();
  const { modals } = useProfileModals();

  const canEditProfile =
    roles.includes(PAGE_ROLES.ADMIN.value) ||
    roles.includes(PAGE_ROLES.EDITOR.value);

  const visibleActionButton =
    (isAuthUser && !modals.selectedForEdit) ||
    (hasMembership && canEditProfile);

  return visibleActionButton;
};

export default useVisibleProfileSectionActionButton;
