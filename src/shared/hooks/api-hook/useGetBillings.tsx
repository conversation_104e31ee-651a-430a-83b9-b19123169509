import React from 'react';
import { Billing } from '@shared/components/Organism/Billing/types';
import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import { QueryKeys } from '@shared/utils/constants';
import { getJobBillingList } from '@shared/utils/api/jobs';
import { UseInfiniteQueryOptions } from '@tanstack/react-query';

export default function useGetBillings(
  billingType: Billing['billingType'],
  config: UseInfiniteQueryOptions = {}
) {
  const jobBillingQuery = useReactInfiniteQuery(
    [QueryKeys.getBillingList, billingType],
    {
      func: getJobBillingList,
    },
    {
      ...config,
      enabled: billingType === 'PUBLISH_JOB',
    }
  );
  return jobBillingQuery;
}
