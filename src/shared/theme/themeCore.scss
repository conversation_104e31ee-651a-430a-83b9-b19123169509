$theme-colors: (
  'sky': var(--sky),
  'coal': var(--coal),
  'cloud': var(--cloud),
  'black': var(--black),
  'black_10': var(--black_10),
  'black_16': var(--black_16),
  'black_20': var(--black_20),
  'black_30': var(--black_30),
  'black_50': var(--black_50),
  'black_40': var(--black_40),
  'black_80': var(--black_80),
  'brand': var(--brand),
  'darkBrand': var(--darkBrand),
  'darkBrandHover': var(--darkBrandHover),
  'brand_10': var(--brand_10),
  'busbrand_10': var(--busbrand_10),
  'brand_20': var(--brand_20),
  'brand_30': var(--brand_30),
  'brand_70': var(--brand_70),
  'barBgLight': var(--barBgLight),
  'blueFog': var(--blueFog),
  'arcticBlue': var(--arcticBlue),
  'darkSecondary': var(--darkSecondary),
  'popOverBg': var(--popOverBg),
  'gray': var(--gray),
  'gray_5': var(--gray_5),
  'gray_10': var(--gray_10),
  'gray_30': var(--gray_30),
  'gray_20': var(--gray_20),
  'gray_50': var(--gray_50),
  'gray_60': var(--gray_60),
  'gray_70': var(--gray_70),
  'hover': var(--hover),
  'hover_2': var(--hover_2),
  'hover_50': var(--hover_50),
  'hoverGray_15': var(--hoverGray_15),
  'iceBlue': var(--iceBlue),
  transparent: var(--transparent),
  'error': var(--error),
  'error_4': var(--error_4),
  'error_5': var(--error_5),
  'error_10': var(--error_10),
  'error_20': var(--error_20),
  'error_50': var(--error_50),
  'disabledError': var(--disabledError),
  'orange': var(--orange),
  'pendingOrange': var(--pendingOrange),
  'pendingOrange_4': var(--pendingOrange_4),
  'pendingOrange_10': var(--pendingOrange_10),
  'pendingOrange_50': var(--pendingOrange_50),
  'muteMidGray': var(--muteMidGray),
  white: var(--white),
  'white_10': var(--white_10),
  'white_20': var(--white_20),
  'white_40': var(--white_40),
  'white_50': var(--white_50),
  'white_80': var(--white_80),
  'background2Light': var(--background2Light),
  'mint': var(--mint),
  'mint_10': var(--mint_10),
  'mint_30': var(--mint_30),
  'navyBlue': var(--navyBlue),
  'navyBlue_10': var(--navyBlue_10),
  'navHover_10': var(--navHover_10),
  'graphene': var(--graphene),
  'graphene_60': var(--graphene_60),
  'techGray_20': var(--techGray_20),
  'techGray_10': var(--techGray_10),
  'hover_75': var(--hover_75),
  'disabledGray': var(--disabledGray),
  'disabledGrayDark': var(--disabledGrayDark),
  'highlightIcon': var(--highlightIcon),
  'warning': var(--warning),
  'irrelevant': var(--irrelevant),
  'irrelevant_10': var(--irrelevant_10),
  'irrelevant_30': var(--irrelevant_30),
  'success': var(--success),
  'success_5': var(--success_5),
  'success_4': var(--success_4),
  'success_10': var(--success_10),
  'snow': var(--snow),
  'snow_90': var(--snow_90),
  'trench': var(--trench),
  'warning_5': var(--warning_5),
  'warning_10': var(--warning_10),
  'warning_30': var(--warning_30),
  'brand_5': var(--brand_5),
  'brand_4': var(--brand_4),
  'background2Dark': var(--background2Dark),
  'lightGray': var(--lightGray),
  'brown': var(--brown),
  'brown_4': var(--brown_4),
  'lightboxBackdrop': var(--lightboxBackdrop),
  'lightGreen': var(--lightGreen),
  'lightError': var(--lightError),
  'borderError': var(--borderError),
  'darkHover': var(--darkHover),
  'darkHover_2': var(--darkHover_2),
  'lead': var(--lead),
  'lightBrownDark': var(--lightBrownDark),
  'cornflowerBlue': var(--cornflowerBlue),
  'cornflowerBlueDark': var(--cornflowerBlueDark),
  'heliotrope': var(--heliotrope),
  'heliotropeDark': var(--heliotropeDark),
  'darkTangerine': var(--darkTangerine),
  'darkTangerineDark': var(--darkTangerineDark),
  'darkError': var(--darkError),
  'darkErrorDark': var(--darkErrorDark),
  'background': var(--background),
  'background2': var(--background2),
  'background3': var(--background3),
  'background4': var(--background4),
  'background6': var(--background6),
  'background7': var(--background7),
  'background8': var(--background8),
  'background9': var(--background9),
  'background10': var(--background10),
  'background11': var(--background11),
  'backgroundIcon': var(--backgroundIcon),
  'backgroundIconSecondary': var(--backgroundIconSecondary),
  'colorIcon': var(--colorIcon),
  'colorIconSecond': var(--colorIconSecond),
  'colorIconThird': var(--colorIconThird),
  'colorIconForth': var(--colorIconForth),
  'colorIconForth2': var(--colorIconForth2),
  'colorIconFifth': var(--colorIconFifth),
  'colorIconSixth': var(--colorIconSixth),
  'colorIconSeventh': var(--colorIconSeventh),
  'colorIconEighth': var(--colorIconEighth),
  'colorIconNinth': var(--colorIconNinth),
  'colorIconTen': var(--colorIconTen),
  'border': var(--border),
  'borderSecond': var(--borderSecond),
  'borderThird': var(--borderThird),
  'borderForth': var(--borderForth),
  'borderFifth': var(--borderFifth),
  'borderSixth': var(--borderSixth),
  'borderSeventh': var(--borderSeventh),
  'borderEighth': var(--borderEighth),
  'muteMidGray_techGray_20': var(--muteMidGray_techGray_20),
  'muteMidGray_coal': var(--muteMidGray_coal),
  'smoke_coal': var(--smoke_coal),
  'smoke_coal_30': var(--smoke_coal_30),
  'primaryText': var(--primaryText),
  'secondaryText': var(--secondaryText),
  'brand5_gray10': var(--brand5_gray10),
  'thirdText': var(--thirdText),
  'forthText': var(--forthText),
  'fifthText': var(--fifthText),
  'sixthText': var(--sixthText),
  'tenthText': var(--tenthText),
  'brand20_brand': var(--brand20_brand),
  'brand_trench': var(--brand_trench),
  'brand_white': var(--brand_white),
  'smoke_brand': var(--smoke_brand),
  'disabledGray_muteMidGray': var(--disabledGray_muteMidGray),
  'disabledGray_highlightIcon': var(--disabledGray_highlightIcon),
  'tooltipText': var(--tooltipText),
  'leadText': var(--leadText),
  'seventhText': var(--seventhText),
  'eighthText': var(--eighthText),
  'ninthText': var(--ninthText),
  'primaryDisabledText': var(--primaryDisabledText),
  'primaryDisabledTextReverted': var(--primaryDisabledTextReverted),
  'gray_techGray20': var(--gray_techGray20),
  'secondaryDisabledText': var(--secondaryDisabledText),
  'barProgress': var(--barProgress),
  'barBg': var(--barBg),
  'smoke': var(--smoke),
  'smoke2': var(--smoke2),
  'smoke_trench': var(--smoke_trench),
  'muteMidGray_disabledGray': var(--muteMidGray_disabledGray),
  'modalHeaderBackground': var(--modalHeaderBackground),
  'tableRowBgOdd': var(--tableRowBgOdd),
  'tableRowBgEven': var(--tableRowBgEven),
  'hoverPrimary': var(--hoverPrimary),
  'hoverPrimary2': var(--hoverPrimary2),
  'hover75_hoverGray15': var(--hover75_hoverGray15),
  'hoverThird': var(--hoverThird),
  'hoverSecondary': var(--hoverSecondary),
  'skeletonBg': var(--skeletonBg),
  'thumbnailHover': var(--thumbnailHover),
  'linkHoverColor': var(--linkHoverColor),
  'inputPlaceholder': var(--inputPlaceholder),
  'input': var(--input),
  'tagHover': var(--tagHover),
  'white2040': var(--white2040),
  'darkHeaderBg_gray_5': var(--darkHeaderBg_gray_5),
  'darkHeaderBg_snow': var(--darkHeaderBg_snow),
  'snow_brand70': var(--snow_brand70),
  'topBarShadow': var(--topBarShadow),
  'hover2_coal': var(--hover2_coal),
  'darkHover_hover': var(--darkHover_hover),
  'darkHover2_hover': var(--darkHover2_hover),
  'hover_75_hover': var(--hover_75_hover),
  'black80_white80': var(--black80_white80),
  'black50_white50': var(--black50_white50),
  'headerBg_brand10': var(--headerBg_brand10),
  'darkSecondary_hover_50': var(--darkSecondary_hover_50),
  'disabledGrayDark_coal': var(--disabledGrayDark_coal),
  'hover_75_background2Light': var(--hover_75_background2Light),
  'darkSecondary_autofillLight': var(--darkSecondary_autofillLight),
  'trench_sky': var(--trench_sky),
  'background2Light_White': var(--background2Light_White),
  'background2Light_coal': var(--background2Light_coal),
  'darkHeaderBg_white': var(--darkHeaderBg_white),
  'darkSecondary_hoverGray15': var(--darkSecondary_hoverGray15),
  'darkSecondary_hover': var(--darkSecondary_hover),
  'smoke_gray': var(--smoke_gray),
  'darkHeaderBg_hoverGray15': var(--darkHeaderBg_hoverGray15),
  'brand_brand20': var(--brand_brand20),
  'brand10_brand20': var(--brand10_brand20),
  'white_hover2': var(--white_hover2),
  'darkSecondary_brand10': var(--darkSecondary_brand10),
  'darkSecondary_brand20': var(--darkSecondary_brand20),
  'disabledGray_hover': var(--disabledGray_hover),
  'disabledGray_coal': var(--disabledGray_coal),
  'trench_brand20': var(--trench_brand20),
  'hover2_graphene': var(--hover2_graphene),
  'white_popOverBg': var(--white_popOverBg),
  'popOverBg_white': var(--popOverBg_white),
  'smoke_hover75': var(--smoke_hover75),
  'coal_disabledGray': var(--coal_disabledGray),
  'graphene_hover': var(--graphene_hover),
  'gray_graphene': var(--gray_graphene),
  'hover75_techGray_20': var(--hover75_techGray_20),
  'darkSecondary_gray10': var(--darkSecondary_gray10),
  'darkSecondary_gray': var(--darkSecondary_gray),
  'darkSecondary_20_gray_10': var(--darkSecondary_20_gray_10),
  'background2Light_60_popOverBg_60': var(--background2Light_60_popOverBg_60),
  'smoke_lead': var(--smoke_lead),
  'disabledGrayDark_disabledGray': var(--disabledGrayDark_disabledGray),
  'darkSecondary10_white10': var(--darkSecondary10_white10),
  'gray10_hover2': var(--gray10_hover2),
  'muteMidGray_gray80': var(--muteMidGray_gray80),
  'lightBrown': var(--lightBrown),
  'muteMidGray_hover_6': var(--muteMidGray_hover_6),
  'blueGradientLayover': var(--blueGradientLayover),
  'blueGradientLayoverHover': var(--blueGradientLayoverHover),
  'modalBlur': var(--modalBlur),
  'popoverBg_hover': var(--popoverBg_hover),
  'white_04_black_05': var(--white_04_black_05),
  'like-bg': var(--like-bg),
  'boost-bg': var(--boost-bg),
  'celeb-bg': var(--celeb-bg),
  'ban-bg': var(--ban-bg),
  'disabledGrayDark_gray': var(--disabledGrayDark_gray),
  'disabledGray_graphene': var(--disabledGray_graphene),
  'wash': var(--wash),
  'hover75_hover2': var(--hover75_hover2),
  'pale_background': var(--pale_background),
  'white_60': var(--white_60),
  'little_transparent_background': var(--little_transparent_background),
  'ban_bg': var(--ban_bg),
  'background2_dark_light': var(--background2_dark_light),
  'celeb_bg_dark_light': var(--celeb_bg_dark_light),
  'booster': var(--booster),
  'darkBackground_background2light': var(--darkBackground_background2light),
  'graphene60_gray60': var(--graphene60_gray60),
  'gold': var(--gold),
  'goldHover': var(--goldHover),
  'brownish': var(--brownish),
  'success_20': var(--success_20),
  'pendingOrange_20': var(--pendingOrange_20),
  'glassEffect_50': var(--glassEffect_50),
  'glassEffect_70': var(--glassEffect_70),
  'semi_brand': var(--semi_brand),
  'module_brand': var(--module_brand),
  'module_brand_4': var(--module_brand_4),
  'module_brand_10': var(--module_brand_10),
  'module_brand_20': var(--module_brand_20),
  'module_semi_brand': var(--module_semi_brand),
  'module_darkBrand': var(--module_darkBrand),
  'module_darkBrandHover': var(--module_darkBrandHover),
  'module_trench': var(--module_trench),
);

@function colors($color-name) {
  @return map-get($theme-colors, $color-name);
}

$views-values: (
  'XS_MOBILE': 320px,
  'MID_MOBILE': 375px,
  'MID_TABLET': 768px,
  'smallMobileBreakpoint': 320px,
  'tabletBreakpoint': 1124px,
  'midDesktopBreakpoint': 1312px,
  'desktopBreakpoint': 1440px,
);

$breakpoints-values: (
  'XS_MOBILE': map-get($views-values, XS_MOBILE),
  'MID_MOBILE': map-get($views-values, MID_MOBILE),
  'MID_TABLET': map-get($views-values, MID_TABLET),
  'smallMobile': map-get($views-values, smallMobileBreakpoint),
  'tablet': map-get($views-values, tabletBreakpoint),
  'midDesktop': map-get($views-values, midDesktopBreakpoint),
  'desktop': map-get($views-values, desktopBreakpoint),
);

@function breakpoints($breakpoint) {
  @return map-get($breakpoints-values, $breakpoint);
}

@function views($breakpoint) {
  @return map-get($views-values, $breakpoint);
}

$variables-values: (
  'gutter': 16px,
  'desktopGutter': 32px,
  'largeGutter': 20px,
  'xLargeGutter': 24px,
  'sidePanelWidth': 72px,
  'leftPanelMaxWidth': 260px,
  'chatPanelMaxWidth': 342px,
  'headerPopperMenuWidth': 324px,
  'avatarSize': 34px,
  'avatarSizeBig': 152px,
  'mainHeaderDesktopHeight': 72px,
  'headerMobileHeight': 56px,
  'headerDesktopHeight': 64px,
  'logoMinHeight': 24px,
  'logoMaxHeight': 32px,
  'contentMaxWidth': 980px,
  'wideContentMaxWidth': 1170px,
  'mainContentMaxWidth': 650px,
  'rightPaneMaxWidth': 270px,
  'chatBubbleMaxWidth': 277px,
  'bottomBarHeight': 48px,
  'rightSideModalMaxWidth': 400px,
  'wideRightSideModalMaxWidth': 650px,
  'doubleRightSideModalMaxWidth': 80%,
  'headerItemsGap': 39.5px,
  'safeAreaInsetBottom': var(--l-safe-area-bottom),
  'iconSize': 24px,
);

@function variables($variable) {
  @return map-get($variables-values, $variable);
}

$SafeArea: variables(safeAreaInsetBottom);

$font-values: (
  'thin': 'Roboto-Thin',
  'regular': var(--font-roboto-regular),
  'medium': var(--font-roboto-medium),
  'bold': var(--font-roboto-bold),
  'light': var(--font-roboto-light),
  'black': 'Roboto-Black',
  'italic': 'Roboto-Italic',
);

@function fonts($font) {
  @return map-get($font-values, $font);
}

$zIndex-values: (
  'layout': 500,
  'layoutMobile': 1050,
  'confirmation': 1000,
  'popperMenu': 2000,
  'popover': 2100,
  'snackbar': 2500,
  'tooltip': 3000,
  'overlay': 3500,
);

@function zIndex($zIndex) {
  @return map-get($zIndex-values, $zIndex);
}

$transition-values: (
  'smooth': all 200ms ease-in-out,
);

@function transitions($transition) {
  @return map-get($transition-values, $transition);
}

$hoverEffect-values: (
  'cardShadow': 0 0 30px colors(white_04_black_05),
  'cardBorder': 1px solid var(--palette-gray_20),
);

@function hoverEffects($hoverEffect) {
  @return map-get($hoverEffect-values, $hoverEffect);
}

$contentHeight-values: (
  'mobile': 84px,
  'tablet': 108px,
  'desktop': 144px,
);

@function contentHeight($contentHeight) {
  @return map-get($contentHeight-values, $contentHeight);
}
