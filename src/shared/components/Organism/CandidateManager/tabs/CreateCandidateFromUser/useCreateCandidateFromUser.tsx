import { useMemo, useState } from 'react';
import type {
  CandidateFormData,
  CreateCandidateFormData,
} from '@shared/types/candidates';
import useTranslation from '@shared/utils/hooks/useTranslation';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import useToast from '@shared/uikit/Toast/useToast';
import {
  candidateBasicInfoNormalizer,
  transformCandidateFormData,
} from '@shared/components/Organism/MultiStepForm/CreateCandidateForm/steps/Step1.NewCandidate/useNewCandidateStep';
import { createCandidate } from '@shared/utils/api/candidates';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import { CandidateFormStepKeys } from '@shared/components/Organism/MultiStepForm/CreateCandidateForm/constants';
import { renderNotificationText } from '@shared/components/Organism/Notification/utils/renderNotificationText';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import { CANDIDATE_STATUS_VALUES } from '@shared/utils/constants/enums';
import { useQueryClient } from '@tanstack/react-query';
import { QueryKeys } from '@shared/utils/constants';
import { useManagerContext } from '../../CandidateManager.context';

export default function useCreateCandidateFromUser() {
  const { candidate, isLoading } = useManagerContext();
  const { t } = useTranslation();
  const toast = useToast();
  const { handleChangeParams } = useCustomParams();

  const [isCreating, setCreating] = useState(false);
  const queryClient = useQueryClient();
  const queries = queryClient
    .getQueryCache()
    .findAll({ queryKey: [QueryKeys.searchCandidates] });

  const updateListQueries = (data: CandidateFormData) => {
    queries.forEach((query) => {
      const key = query.queryKey;
      queryClient.setQueryData(key, (prevData: any) => {
        if (!prevData || !prevData.content) return prevData;
        return {
          ...prevData,
          content: prevData.content.map((item: CandidateFormData) =>
            item.id === candidate?.id ? { ...item, id: data.id } : item
          ),
        };
      });
    });
  };

  const onCreateCandidate = (data?: CandidateFormData) => {
    if (data?.id && data?.id !== candidate?.id) {
      updateListQueries(data);
      handleChangeParams({
        replace: { currentEntityId: data.id },
      });
    }
  };
  const handleAdd = async () => {
    setCreating(true);
    if (!candidate) return;
    const body = transformCandidateFormData(
      candidateBasicInfoNormalizer(candidate) as CreateCandidateFormData
    );
    const responseData = await createCandidate({ body });
    if (responseData) {
      toast({
        type: 'success',
        icon: 'check-circle',
        title: t('candidate_added'),
        message: t('candidate_added_message'),
      });
      onCreateCandidate(responseData);
      setCreating(false);
    }
  };
  const handleEditAndAdd = () => {
    if (!candidate) return;
    openMultiStepForm({
      formName: 'createCandidateForm',
      stepKey: CandidateFormStepKeys.NEW,
      data: creationDataNormalizer(candidate),
      options: { onCreate: onCreateCandidate },
    });
  };
  const content = useMemo(
    () =>
      renderNotificationText(
        translateReplacer(t('d_y_w_add_user_to_candidate'), [
          candidate?.profile?.fullName || '',
        ]),
        [candidate?.profile?.fullName || '']
      ),
    [candidate?.profile?.fullName]
  );
  return { isLoading, isCreating, content, t, handleAdd, handleEditAndAdd };
}

const creationDataNormalizer = ({
  id,
  ...rest
}: CandidateFormData): Omit<CandidateFormData, 'id'> => ({
  ...rest,
  openToWork: CANDIDATE_STATUS_VALUES.PREFER_NOT_TO_SAY,
});
