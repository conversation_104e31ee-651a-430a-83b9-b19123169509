'use client';

import React, { useEffect } from 'react';
import { createPage, updatePage } from 'shared/utils/api/page';
import beforeCachePageDetail from 'shared/utils/normalizers/beforeCachePageDetail';
import getCookieKey from 'shared/utils/toolkit/getCookieKey';
import Cookies from 'shared/utils/toolkit/cookies';
import { routeNames } from 'shared/utils/constants/routeNames';
import formValidator, {
  phoneNumberValidation,
} from 'shared/utils/form/formValidator';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from 'shared/hooks/useMultiStepForm';
import { useProfile } from '@shared/contexts/profile.provider';
import useProfilePage from 'shared/hooks/useProfilePage';
import useHistory from 'shared/utils/hooks/useHistory';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import descriptionLengthValidator from 'shared/constants/descriptionLengthValidator';
import { usePathname, useRouter } from 'next/navigation';
import { PROFILE_SCROLL_WRAPPER } from 'shared/constants/enums';
import useIsMounted from 'shared/hooks/useIsMounted';
import useUpdateQueryData from '@shared/utils/hooks/useUpdateQueryData';
import { QueryKeys } from '@shared/utils/constants';
import useShowSuccessToast from '../useShowSuccessToast';
import MultiStepForm from '../MultiStepForm';
import type { MultiStepFormProps } from '../MultiStepForm';
import { useCreatePageFormMultiStepForm } from './useCreatePageFormMultiStepForm';
import classes from './useCreatePageFormMultiStepForm.module.scss';

// Note: Redirection in onSuccess or onClose can disrupt the modal functionality. use Timeout if necessary.
const CreatePageFormMultiStepForm: React.FC<{
  userName?: string;
  onSuccessCallback?: (data: never) => void;
  onCloseCallback?: () => void;
}> = ({ userName: username, onSuccessCallback, onCloseCallback }) => {
  const { t } = useTranslation();
  const isMounted = useIsMounted();
  const pathname = usePathname();
  const data = useCreatePageFormMultiStepForm({ username });
  const { setPageCreationFormData } = useProfile();
  const history = useHistory();
  const router = useRouter();
  const { updateBusinessPageQueryCache, reFetchAppObject } = useGetAppObject();
  const { removeQueries: removeMyPagesQueries } = useUpdateQueryData([
    QueryKeys.myPages,
  ]);
  const {
    reFetchPageDetail,
    objectDetail: pageDetail,
    isPageCreation,
  } = useProfilePage({ username });
  const isEdit = !!pageDetail && !isPageCreation;

  const createPageForm = useMultiStepFormState('createPageForm');
  const {
    isSingleStep,
    stepKey: initialStepKey,
    focusedField,
  } = createPageForm;
  const showSuccessToast = useShowSuccessToast();
  const stepIndex = data?.findIndex((item) => item?.stepKey === initialStepKey);
  const initialStep = stepIndex > -1 ? stepIndex : undefined;
  const createPageRoute = routeNames.pageCreation;

  const onClose = () => {
    onCloseCallback?.();
    closeMultiStepForm('createPageForm');
    if (pathname === createPageRoute) {
      history.goBack();
    }
  };

  useEffect(() => {
    if (isMounted && createPageForm.isOpen) {
      closeMultiStepForm('createPageForm');
    }
  }, [pathname]);

  useEffect(() => {
    const element = document?.getElementById(PROFILE_SCROLL_WRAPPER);
    element?.classList?.add(classes.forcePushLeft);

    return () => {
      element?.classList?.remove(classes.forcePushLeft);
    };
  }, []);

  const onSuccess = React.useCallback(
    (setStep: any, data: any) => {
      closeMultiStepForm('createPageForm');
      onSuccessCallback?.(data as never);
      const { username, pageLink, usernameAtSign, title, category } =
        beforeCachePageDetail(data);
      router.prefetch(pageLink);
      removeMyPagesQueries();

      if (!isEdit) {
        showSuccessToast({
          isEdit: false,
          variant: 'page',
        });
        return history.push(`/${username}`);
      }
      if (
        username &&
        pageDetail?.username &&
        username !== pageDetail.username
      ) {
        const TOKEN = getCookieKey('businessId');
        Cookies.set(TOKEN, username);
        history.push(`/${username}`);
      }
      reFetchAppObject();
      updateBusinessPageQueryCache((prevData) => ({
        ...prevData,
        username,
        pageLink,
        usernameAtSign,
        title,
        category,
      }));

      reFetchPageDetail(username);
    },
    [isEdit, pageDetail]
  );

  const getValidationSchema: MultiStepFormProps['getValidationSchema'] =
    React.useCallback(
      ({ step }) => {
        const validationSchema = {
          0: {
            username: formValidator
              .string()
              .test(
                'short',
                t('USERNAME_TOO_SHORT_EXCEPTION'),
                (username) => (username?.length || 0) > 4
              ),
          },
          1: {
            industry: formValidator
              .object()
              .test('industry', 'select_one_of_sug_cate', (val) => val?.value),
            language: formValidator
              .object()
              .test('language', 'select_one_of_sug_cate', (val) => val?.value),
            description: descriptionLengthValidator,
          },
          2: {
            link: formValidator
              .string()
              .nullable()
              .matches(
                /((https?):\/\/)?(www.)?[a-z0-9-]+(\.[a-z]{2,}){1,3}(#?\/?[a-zA-Z0-9#-]+)*\/?(\?[a-zA-Z0-9-_]+=[a-zA-Z0-9-%]+&?)?$/,
                'enter_valid_url'
              ),
            email: formValidator.string().nullable().email('enter_valid_email'),
            phone: phoneNumberValidation,
          },
          3: {
            locations: formValidator
              .array()
              .of(
                formValidator.object().shape({
                  dayOfWeek: formValidator.string(),
                  checked: formValidator.boolean(),
                })
              )
              .required(t('required_lg')),
          },
        } as const;

        return formValidator.object().shape(validationSchema[step]);
      },
      [formValidator]
    );
  const apiFunc: MultiStepFormProps['apiFunc'] = isEdit
    ? updatePage
    : createPage;

  const transform: MultiStepFormProps['transform'] = ({
    id,
    username,
    title,
    status,
    description,
    establishmentDate,
    email,
    phone,
    link,
    hashtags,
    category,
    industry,
    companySize,
    locations,
    language,
    imageUrl,
    profileImages = {},
  }: any) => ({
    id,
    username,
    title,
    status,
    description,
    establishmentDate,
    email,
    phone,
    link,
    hashtags,
    imageUrl,
    ...profileImages,
    locations: locations?.map(
      ({ id, location, ...locationsRest }: any, index: number) => ({
        ...location,
        ...locationsRest,
        primaryLocation: index === 0,
      })
    ),
    category: category?.value,
    companySize: companySize?.value,
    industryName: industry?.label,
    industryLookupId: industry?.value,
    languageName: language?.label,
    languageLookupId: language?.value,
  });

  const initialValues =
    pageDetail && !isPageCreation
      ? {
          ...pageDetail,
        }
      : {};
  // No need to change these
  const totalSteps: MultiStepFormProps['totalSteps'] = data?.length;
  const getHeaderProps = getStepData('getHeaderProps', data);
  const getStepHeaderProps = getStepData('getStepHeaderProps', data);

  const renderBody = (args) => {
    const { values } = args;
    setPageCreationFormData({
      ...values,
      usernameAtSign: values?.username ? `@${values?.username}` : '@username',
      category: values?.category || { label: t('page_type') },
      title: values?.title || t('page_title'),
    });
    return getStepData('renderBody', data)(args);
  };
  const renderFooter = getStepData('renderFooter', data);

  return (
    <MultiStepForm
      getValidationSchema={getValidationSchema}
      apiFunc={apiFunc}
      totalSteps={totalSteps}
      initialValues={initialValues}
      getHeaderProps={getHeaderProps}
      renderBody={renderBody}
      renderFooter={renderFooter}
      getStepHeaderProps={getStepHeaderProps}
      transform={transform}
      isSingleStep={isSingleStep}
      initialStep={initialStep}
      onClose={onClose}
      onSuccess={onSuccess}
      focusedField={focusedField}
      wide={false}
      formName="createPageForm"
      confirmStyles={{ wrapper: classes.confirmWrapper }}
      bodyProps={{ className: classes.modalBody }}
    />
  );
};

export default CreatePageFormMultiStepForm;

function getStepData<T extends keyof MultiStepFormProps>(
  key: T,
  data: MultiStepFormProps[]
): MultiStepFormProps[T] {
  return ({ step, ...rest }: any) => data[step][key]?.({ step, ...rest });
}
