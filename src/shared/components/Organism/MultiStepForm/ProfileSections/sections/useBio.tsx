import React from 'react';
import Endpoints from 'shared/utils/constants/endpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import formValidator from 'shared/utils/form/formValidator';

import removeRichTextBreakLines from 'shared/utils/toolkit/removeRichTextBreakLines';
import { useProfile } from '@shared/contexts/profile.provider';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import { closeMultiStepForm } from 'shared/hooks/useMultiStepForm';
import { DESCRIPTION_MAX_LENGTH } from 'shared/constants/enums';
import { useGetAboutSectionsData } from 'shared/hooks/api-hook';
import type { SingleDataItem } from '../useEditProfileSectionsMultiStepForm';
import { profileSectionsStepKeys } from '../constants';
import { TwoButtonFooter } from '../Components/TwoButtonFooter';
import { useUtils } from './utils/useUtils';
import useShowSuccessToast from '../../useShowSuccessToast';
import classes from './styles.module.scss';

const useBio = (): SingleDataItem => {
  const {
    data: user,
    isSingle,
    handleBackWithConfirm,
  } = useUtils({ variant: 'onePageOnly' });
  const { refetch } = useGetAboutSectionsData();

  const { t } = useTranslation();
  const { reFetchAboutSectionQuery } = useProfile();
  const showSuccessToast = useShowSuccessToast();

  const initialValues = {
    bio: user?.bio,
  };

  const onSuccess = (setStep: any) => {
    showSuccessToast({
      isEdit: true,
      variant: 'profile',
    });
    reFetchAboutSectionQuery?.();
    refetch?.();
    if (isSingle) {
      closeMultiStepForm('editProfileSections');
    } else {
      setStep((prev: number) => prev - 1);
    }
  };

  return {
    stepKey: profileSectionsStepKeys.BIO,
    url: Endpoints.App.User.Update.biography,
    method: 'PUT',
    onSuccess,
    initialValues,
    getValidationSchema: getBioValidationSchema,
    transform: transformBio,
    getHeaderProps: ({ setStep, dirty }) => ({
      title: isSingle ? t('edit_bio') : t('edit_sections'),
      hideBack: !!isSingle,
      backButtonProps: {
        onClick: () => handleBackWithConfirm()({ setStep, dirty }),
      },
      noCloseButton: !isSingle,
    }),
    renderBody: () => (
      <DynamicFormBuilder
        className={classes.grow}
        groups={getBioGroups({ t })}
      />
    ),
    renderFooter: ({ setStep, dirty }) => (
      <TwoButtonFooter
        secondaryButtonOnClick={() =>
          handleBackWithConfirm()({ setStep, dirty })
        }
      />
    ),
  };
};

export default useBio;

export function getBioGroups({ t }: any) {
  return [
    {
      name: 'bio',
      cp: 'richtext',
      label: t('write_your_short_resume'),
      visibleOptionalLabel: false,
      wrapStyle: classes.grow,
      className: classes.bioRichText,
      maxLength: DESCRIPTION_MAX_LENGTH,
    },
  ];
}

export function getBioValidationSchema() {
  return formValidator.object().shape({
    bio: formValidator
      .string()
      .test('len', 'bio_max_length', (val) => (val?.length || 0) < 4800),
  });
}

export const transformBio = ({ bio }: any) => ({
  bio: removeRichTextBreakLines(bio),
});
