import { useCallback, useEffect, useRef } from 'react';
import {
  closeMultiStepForm,
  openMultiStepForm,
  useMultiStepFormState,
} from 'shared/hooks/useMultiStepForm';
import useOpenConfirm from 'shared/uikit/Confirmation/useOpenConfirm';
import useTranslation from 'shared/utils/hooks/useTranslation';
import event from 'shared/utils/toolkit/event';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import { useGetAboutSectionsData } from 'shared/hooks/api-hook';
import eventKeys from 'shared/constants/event-keys';
import { useProfile } from '@shared/contexts/profile.provider';
import useShowSuccessToast from 'shared/components/Organism/MultiStepForm/useShowSuccessToast';
import { openHighlightModal } from './useHighLightModal';
import { useDiscardConfirm } from './useDiscardConfirm';
import { useActiveState } from './useActiveState';

type Variant = 'normal' | 'onePageOnly';

export const useUtils = ({
  variant = 'normal',
  url,
  highlightDataTransformer,
  customizedOnSuccess,
}: {
  variant?: Variant;
  url?: string;
  highlightDataTransformer?: (data: any, values: any) => any;
  customizedOnSuccess?: (...args: any) => any;
}) => {
  const [activeState, setActiveState, activeItem, setActiveItem] =
    useActiveState();
  const { reFetchAboutSectionQuery } = useProfile();
  const { openConfirm } = useDiscardConfirm();
  const state = useMultiStepFormState('editProfileSections');
  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
  });
  const { t } = useTranslation();
  const { data, refetch } = useGetAboutSectionsData();
  const capturedIsSingle = useRef<boolean | undefined>(undefined);
  const showSuccessToast = useShowSuccessToast();

  const editUrl = `${url}/${activeItem?.id}`;

  const { mutate: deleteItem } = useReactMutation({
    url: editUrl,
    headers: {},
    method: 'DELETE',
    onSettled: async () => {
      await refetch();
      reFetchAboutSectionQuery?.();
    },
  });

  const backToList = useCallback(() => {
    setActiveState('list');
  }, [setActiveState]);

  const onSuccess = async (...args: any) => {
    const data = highlightDataTransformer?.(args?.[2], args?.[3]);
    reFetchAboutSectionQuery?.();
    refetch();
    if (customizedOnSuccess) {
      customizedOnSuccess(...args);
    } else {
      showSuccessToast({
        isEdit: true,
        variant: 'profile',
      });
    }

    if (highlightDataTransformer) {
      openHighlightModal({ data });
      capturedIsSingle.current = !!isSingle;
    } else {
      capturedIsSingle.current = !!isSingle;
      continueOnSuccess();
    }
    setTimeout(backToList, 0);
  };

  const continueOnSuccess = useCallback(() => {
    if (typeof capturedIsSingle.current !== 'boolean') return;
    if (variant === 'onePageOnly') {
      return backToList();
    }
    if (capturedIsSingle.current) {
      closeMultiStepForm('editProfileSections');
    } else {
      backToList();
    }
    capturedIsSingle.current = undefined;
  }, [backToList, closeMultiStepForm]);

  useEffect(() => {
    event.on(eventKeys.continueOnSuccessAfterHighlightPopup, continueOnSuccess);

    return () => event.off(eventKeys.continueOnSuccessAfterHighlightPopup);
  }, [continueOnSuccess]);

  const handleBackWithConfirm =
    (parentListKey?: string, callbackGoBackStep?: Function) =>
    ({ setStep, dirty }: any) => {
      const goBackStep = () => {
        callbackGoBackStep?.();
        setStep((prev) => prev - 1);
      };

      if (isSingle) {
        return event.trigger(eventKeys.closeModal);
      }

      if (variant === 'normal') {
        if (activeState === 'list' && !dirty) {
          if (parentListKey) {
            openMultiStepForm({
              formName: 'editProfileSections',
              stepKey: parentListKey,
            });
          } else {
            goBackStep();
          }
        } else {
          const func = activeState === 'list' ? () => goBackStep() : backToList;

          if (dirty) {
            openConfirm(func);
          } else {
            func();
          }
        }
      } else if (variant === 'onePageOnly') {
        if (dirty) {
          openConfirm(() => {
            setActiveState('list');
            goBackStep();
          });
        } else {
          setActiveState('list');
          goBackStep();
        }
      }
    };

  useEffect(() => {
    if (state?.data?.activeState) setActiveState(state?.data?.activeState);
  }, []);

  useEffect(() => {
    if (activeState === 'list') {
      setActiveItem(null);
    }
    event.trigger(eventKeys.resetForm);
  }, [activeState]);

  const isSingle =
    state?.data?.isSingle &&
    (variant === 'normal' ? activeState === state?.data?.activeState : true);

  const handleClick = (items: any[]) => (id: string) => {
    const selected = items?.find((item: any) => item?.id === id);
    setActiveItem(selected?.realData);
    setActiveState('edit');
  };

  const deleteWithConfirm = (apiFunc: Function) => () => {
    openConfirmDialog({
      title: t('delete'),
      message: t('delete_item_confirmation'),
      confirmButtonText: t('delete'),
      cancelButtonText: t('cancel'),
      isAjaxCall: true,
      apiProps: {
        func: apiFunc,
        onSuccess: async () => {
          backToList();
          showSuccessToast({
            isEdit: true,
            variant: 'profile',
          });
        },
      },
    });
  };

  return {
    handleClick,
    deleteWithConfirm,
    isSingle,
    activeItem,
    onSuccess,
    handleBackWithConfirm,
    setActiveItem,
    setActiveState,
    activeState,
    data,
    deleteItem,
    editUrl,
    backToList,
    openDiscardConfirm: openConfirm,
  };
};
