import React from 'react';
import { type FixedRightSideModalDialogProps } from '@shared/uikit/Modal/FixedRightSideModalDialog/FixedRightSideModalDialog.component';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import Form from 'shared/uikit/Form';
import Flex from '@shared/uikit/Flex';
import useTranslation from 'shared/utils/hooks/useTranslation';
import SubmitButton from 'shared/uikit/Form/SubmitButton';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalFooter from 'shared/uikit/Modal/ModalFooter';
import Button from 'shared/uikit/Button';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import useCheckoutForm from './useCheckoutModal';
import InvoiceSummary from './CheckoutModal.invoiceSummary';
import { type CheckoutChildModalData } from '.';
import classes from './index.module.scss';

export interface CheckoutFormProps extends CheckoutChildModalData {
  isOpen: boolean;
  portal?: string;
  modalProps?: Partial<FixedRightSideModalDialogProps>;
}

export default function CheckoutForm({
  modalProps: parentModalProps = {},
  invoice,
  entityData,
  actions,
}: CheckoutFormProps) {
  const {
    initialData,
    groups,
    transform,
    validationSchema,
    apiFunc,
    onSuccess,
    onFailure,
    onCancel,
    modalProps,
  } = useCheckoutForm({
    entityData,
    invoice,
    actions,
    modalProps: parentModalProps,
  });
  const { t } = useTranslation();
  return (
    <Form
      initialValues={initialData}
      onSuccess={onSuccess}
      apiFunc={apiFunc}
      onFailure={onFailure}
      transform={transform}
      validationSchema={validationSchema}
      className={classes.form}
    >
      {(props) => (
        <FixedRightSideModal
          showConfirm
          onClickOutside={onCancel(props.dirty)}
          {...modalProps}
        >
          <div style={{ display: 'contents' }}>
            <ModalHeaderSimple
              title={t('checkout')}
              hideBack={false}
              noCloseButton
              backButtonProps={{ onClick: onCancel(props.dirty) }}
            />
            <ModalBody className={classes.fullHeight}>
              <DynamicFormBuilder groups={groups(props)} />
            </ModalBody>
            <ModalFooter>
              <InvoiceSummary
                formikProps={props}
                {...invoice}
                isLoading={false}
              />
              <Flex className={classes.footer}>
                <Button
                  fullWidth
                  label={t('discard')}
                  schema="gray-semi-transparent"
                  onClick={onCancel(props.dirty)}
                />
                <SubmitButton
                  fullWidth
                  schema="primary-blue"
                  labelFont="bold"
                  label={t('complete_checkout')}
                />
              </Flex>
            </ModalFooter>
          </div>
        </FixedRightSideModal>
      )}
    </Form>
  );
}
