import React, { useMemo } from 'react';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import useTranslation from '@shared/utils/hooks/useTranslation';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import Typography from '@shared/uikit/Typography';
import HeroIcon from '@shared/uikit/HeroIcon';
import Flex from '@shared/uikit/Flex';
import Divider from '@shared/uikit/Divider';
import Button from '@shared/uikit/Button';
import type { Billing } from '@shared/components/Organism/Billing/types';
import useGetBillings from '@shared/hooks/api-hook/useGetBillings';
import type { CheckoutChildModalData, PaymentResultData } from '..';
import BillingCard from '../../../Billing/Partials/BillingCard';
import classes from './index.module.scss';

type CheckoutResultModalProps = CheckoutChildModalData & PaymentResultData;

interface InvoiceDetailsResponse {
  price: string;
  invoiceModel?: Billing;
  jobModel?: any;
}

export default function CheckoutResultSuccess({
  entityData,
  invoice,
  status,
  billingId,
  billing,
  title,
  message,
  actions,
  modalProps,
}: CheckoutResultModalProps) {
  const { t } = useTranslation();

  const { data: billingDetailsData } = useGetBillings(entityData.requestType, {
    refetchOnMount: true,
  });
  const selectedBilling = useMemo(
    () =>
      billingDetailsData?.find(
        ({ id }) => id === String(billingId)
      ) as Partial<BillingCardProps>,
    [billingDetailsData, billingId]
  );
  return (
    <FixedRightSideModal isOpen {...modalProps}>
      <ModalBody className="p-20 gap-20 h-full">
        <Flex className="items-center">
          <HeroIcon
            className="mb-20"
            iconName="check"
            color="green"
            size={80}
            iconSize={50}
          />
          <Typography font="700" size={16} className="mb-8">
            {title}
          </Typography>
          <Typography size={14} color="secondaryDisabledText">
            {message}
          </Typography>
        </Flex>
        {selectedBilling && (
          <>
            <Divider />
            <BillingCard data={{ ...entityData, ...selectedBilling }} />
          </>
        )}
      </ModalBody>
      <ModalFooter className={classes.modalFooter}>
        <Button
          onClick={actions?.onCancel}
          label={t('close')}
          schema="gray"
          labelFont="bold"
          className={classes.btn}
        />
        <Button
          onClick={actions?.onSuccess}
          label={t('publish')}
          className={classes.btn}
        />
      </ModalFooter>
    </FixedRightSideModal>
  );
}
