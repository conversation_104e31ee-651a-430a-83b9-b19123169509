import type { CallbackParams } from '@shared/components/Organism/MultiStepForm/MultiStepForm';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import ModalTransitionWrapper from '@shared/uikit/Modal/TransitionWrapper/ModalTransitionWrapper';
import { searchPerson } from '@shared/utils/api/search';
import {
  AGE_RANGE_VALUES,
  DISABILITY_STATUS_VALUES,
  GENDER_VALUES,
  RACE_VALUES,
  VETERAN_STATUS_VALUES,
} from '@shared/utils/constants/enums';
import hereApiResponseNormalizer from '@shared/utils/normalizers/hereApiResponseNormalizer';
import { useMemo } from 'react';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import Endpoints from 'shared/utils/constants/endpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from '../../../CreateCandidate.module.scss';
import { useCandidateModalContext } from '../../../CreateCandidateModalProvider';

const CandidateBackgroundBody: React.FC<CallbackParams> = () => {
  const { t } = useTranslation();
  const { authUser } = useGetAppObject();
  const { isLoboxUser, candidate } = useCandidateModalContext();

  const formGroups = useMemo(
    () => [
      /** LINE 1 */
      {
        name: 'form_group_1',
        formGroup: {
          title: t('residency_n_legal_information'),
          formSection: true,
          className: classes.firstFormGroupLabel,
        },
        wrapStyle: classes.formItemWrapStyle,
        cp: () => null,
      },
      {
        name: 'gender',
        cp: 'dropdownSelect',
        label: t('gender'),
        isFirstHalfWidth: true,
        disabledReadOnly: isLoboxUser || candidate?.genderEnteredByUser,
        options: Object.values(GENDER_VALUES),
      },
      {
        name: 'ageRange',
        cp: 'dropdownSelect',
        label: t('age_range'),
        isSecondHalfWidth: true,
        disabledReadOnly: isLoboxUser || candidate?.ageRangeEnteredByUser,
        options: Object.values(AGE_RANGE_VALUES),
      },
      /** LINE 2 */
      {
        name: 'race',
        cp: 'dropdownSelect',
        label: t('race_ethnicity'),
        disabledReadOnly: isLoboxUser || candidate?.raceEnteredByUser,
        isFirstHalfWidth: true,
        options: Object.values(RACE_VALUES),
      },
      {
        name: 'veteranStatus',
        cp: 'dropdownSelect',
        label: t('veteran_status'),
        disabledReadOnly: isLoboxUser || candidate?.veteranStatusEnteredByUser,
        isSecondHalfWidth: true,
        options: Object.values(VETERAN_STATUS_VALUES),
      },
      /**  LINE 3 */
      {
        name: 'disabilityStatus',
        cp: 'dropdownSelect',
        label: t('disability'),
        disabledReadOnly:
          isLoboxUser || candidate?.disabilityStatusEnteredByUser,
        isFirstHalfWidth: true,
        options: Object.values(DISABILITY_STATUS_VALUES),
      },
      {
        name: 'birthdate',
        cp: 'datePicker',
        isSecondHalfWidth: true,
        disabledReadOnly: isLoboxUser,
        maxDate: new Date(),
        label: t('date_of_birth'),
      },

      {
        formGroup: {
          title: t('referral_information'),
          formSection: true,
          className: classes.lgFormGroupLabel,
        },
        name: 'referralUser',
        label: t('referred_by'),
        cp: 'avatarAsyncAutoComplete',
        apiFunc: searchPerson,
        textInputProps: {
          autocomplete: 'off',
        },
        wrapStyle: classes.formItemWrapStyle,
        maxLength: 100,
        forceVisibleError: true,
        normalizer: (data: { content: any[] }) =>
          hereApiResponseNormalizer(data?.content ?? [])?.filter(
            (item) => item?.value !== authUser?.id
          ),
        visibleRightIcon: true,
      },
      {
        name: 'referralCompany',
        label: t('referral_current_company'),
        cp: 'avatarAsyncAutoComplete',
        isCompany: true,
        maxLength: 100,
        forceVisibleError: true,
        textInputProps: {
          autocomplete: 'off',
        },
        wrapStyle: classes.formItemWrapStyle,
        url: Endpoints.App.Common.suggestPlace,
        normalizer: hereApiResponseNormalizer,
        visibleRightIcon: true,
        rightIconProps: { name: 'search' },
      },
      {
        name: 'referralEmiil',
        textInputProps: {
          autocomplete: 'off',
        },
        forceVisibleError: true,
        label: t('referral_employee_email'),
        wrapStyle: classes.formItemWrapStyle,
        cp: 'input',
      },
      {
        name: 'referralPhone',
        forceVisibleError: true,
        textInputProps: {
          autocomplete: 'off',
        },
        wrapStyle: classes.formItemWrapStyle,
        label: t('referral_contact_number'),
        cp: 'phoneInput',
      },
      {
        name: 'referralUrl',
        label: t('referring_social_url'),
        forceVisibleError: true,
        helperText: t('website_helper'),
        wrapStyle: classes.formItemWrapStyle,
        cp: 'input',
      },
    ],
    [t, isLoboxUser, authUser?.id]
  );

  return (
    <ModalTransitionWrapper className={classes.stepWrapper}>
      <DynamicFormBuilder className={classes.formBuilder} groups={formGroups} />
    </ModalTransitionWrapper>
  );
};

export default CandidateBackgroundBody;
