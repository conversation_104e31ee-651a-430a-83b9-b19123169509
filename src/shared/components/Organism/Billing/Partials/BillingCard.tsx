import React from 'react';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Button from '@shared/uikit/Button';
import Icon from '@shared/uikit/Icon';
import AddBillingCard from './BillingCard.add';
import RefundBillingCard from './BillingCard.refund';
import MainBillingCard from './BillingCard.main';
import JobBillingCard from './BillingCard.job';
import { type BillingCardProps } from '../types';
import classes from './partials.module.scss';

export default function BillingCard({ data, onSelect }: BillingCardProps) {
  const { t } = useTranslation();
  if (data?.billingType === 'ADD_SEAT') return <AddBillingCard data={data} />;
  if (data?.billingType === 'REFUND_SEAT')
    return <RefundBillingCard data={data} />;
  if (data?.billingType === 'PUBLISH_JOB')
    return <JobBillingCard data={data} />;
  return (
    <MainBillingCard data={data}>
      {onSelect && (
        <Button
          variant="text"
          schema="ghost-brand"
          labelClassName={classes.callToActionText}
          label={
            <>
              {t('view_billing_details')}
              <Icon
                name="chevron-right"
                color="brand"
                size={13}
                className="inline-block"
              />
            </>
          }
          rightIcon="chevron-right"
          rightColor="brand"
          rightSize={16}
          onClick={onSelect(data)}
        />
      )}
    </MainBillingCard>
  );
}
