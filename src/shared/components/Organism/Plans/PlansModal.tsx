import React, { type MouseEvent } from 'react';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Button from '@shared/uikit/Button';
import ModalHeaderBase from '@shared/uikit/Modal/BasicModal/Headers/Base/Base.component';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import {
  isPremiumPlan,
  type Plan,
} from '@shared/utils/normalizers/plansNormalizer';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import useMedia from '@shared/uikit/utils/useMedia';
import PlanCard from '../../molecules/PlanCad/PlanCard';
import TimeSpanSwitch from './partails/TimeSpanSwitch';
import classes from './PlansModal.module.scss';

interface PlansModalProps {
  plans?: Plan[];
  onSelect: (plan: Plan) => (event?: MouseEvent<any>) => void;
  onSeeMore: (plan?: Plan) => (event?: MouseEvent<any>) => void;
  handleOpenBillings: (event?: MouseEvent<any>) => void;
  onCancelSubscription: (
    planName?: string
  ) => ((event?: MouseEvent<any>) => void) | undefined;
  onClose?: (e?: any) => void;
  hasPremiumPlan?: boolean;
}

export default function PlansModal({
  plans,
  onSelect,
  onSeeMore,
  onClose,
  onCancelSubscription,
  hasPremiumPlan,
  handleOpenBillings,
}: PlansModalProps) {
  const { t } = useTranslation();
  const { isTabletAndLess } = useMedia();

  return (
    <FixedRightSideModal onClose={onClose} onClickOutside={onClose}>
      <ModalHeaderSimple
        title={t('plans')}
        hideBack={false}
        noCloseButton
        rightContent={() => (
          <Button
            label={t('billing')}
            schema="ghost-brand"
            className="ml-auto"
            onClick={handleOpenBillings}
          />
        )}
        titleProps={{
          className: 'flex-grow',
        }}
      />
      <ModalHeaderBase noCloseButton>
        <TimeSpanSwitch
          items={[
            { label: 'monthly', value: 'MONTHLY' },
            {
              label: 'annually',
              value: 'ANNUALLY',
              promotionBadgeText: 'save_20',
            },
          ]}
        />
      </ModalHeaderBase>
      <ModalBody className={classes.plansListWrapper}>
        {plans?.map((item, idx) => (
          <PlanCard
            key={item?.id || idx}
            {...item}
            onSeeMore={onSeeMore(item)}
            onSelect={isTabletAndLess ? onSelect(item) : undefined}
            buttonProps={
              item?.isActive && isPremiumPlan({ price: item?.price })
                ? {
                    onClick: onCancelSubscription(item?.title),
                    label: t('cancel_subscription'),
                    schema: 'gray-semi-transparent',
                  }
                : undefined
            }
          />
        ))}
      </ModalBody>
      <ModalFooter>
        <Button
          label={t('compare_all_features')}
          schema="semi-transparent"
          rightIcon="chevron-right"
          rightColor="brand"
          rightSize={12}
          onClick={onSeeMore()}
        />
      </ModalFooter>
    </FixedRightSideModal>
  );
}
