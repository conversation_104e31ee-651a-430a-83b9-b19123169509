@import '/src/shared/theme/theme.scss';

.confirmationModalContentWrapper {
  gap: variables(largeGutter);
}
.confirmationModalInfoCardWrapper {
  background-color: colors(gray_5);
}
.confirmationModalInfoCardLabel {
  color: colors(secondaryDisabledText);
}
.timeSpanBadge {
  height: fit-content;
  padding: variables(gutter) / 4;
  .badgeText {
    padding: variables(gutter) / 4;
  }
}
.promotionBadge {
  padding: variables(gutter) / 4 variables(gutter) / 2;
  height: auto;
}
.innerHeaderWrapper {
  flex-direction: row;
  gap: variables(gutter) / 2;
  justify-content: flex-start;
  align-items: center;
}
