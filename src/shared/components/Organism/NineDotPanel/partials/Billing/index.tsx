import React, { type MouseEvent } from 'react';
import type { PageAccessibilityType } from '@shared/types/page';
import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import { jobsEndpoints, QueryKeys } from '@shared/utils/constants';
import { getPlanBillingList } from '@shared/utils/api/page';
import BilingList from '@shared/components/Organism/Billing/BilingList';
import {
  selectData,
  setNineDotPanelState,
  useNineDotPanelState,
} from '@shared/stores/nineDotPanelStore';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import { type Billing } from '@shared/components/Organism/Billing/types';
import BillingDetails from '@shared/components/Organism/Billing/BillingDetails';

interface Props {
  data: PageAccessibilityType[];
  onClose?: (e: any) => void;
}

interface BillingPageDataType {
  selectedBilling?: string;
}

const BillingModal = ({ data, onClose }: Props) => {
  const { selectedBilling } =
    useNineDotPanelState<BillingPageDataType>(selectData);

  const {
    data: billingData,
    isLoading: isLoadingBillingData,
    isFetching: isFetchingBillingData,
  } = useReactInfiniteQuery<Billing>([QueryKeys.getBillingList], {
    func: getPlanBillingList,
    size: 10,
  });

  const { data: billingDetailsData, isLoading: isLoadingBillingDetails } =
    useReactQuery({
      action: {
        key: [QueryKeys.getBillingDetails, selectedBilling],
        url: jobsEndpoints.getJobBillingList,
        // params: {  },
        // spreadParams: true,
      },
      config: {
        enabled: !!selectedBilling,
      },
    });

  const onSelectBilling = (billing: Billing) => (event?: MouseEvent<any>) => {
    setNineDotPanelState({ data: { selectedBilling: billing?.id } });
  };

  const onBack = (event?: MouseEvent<any> | null) => {
    setNineDotPanelState({ data: { selectedBilling: undefined } });
  };

  if (isLoadingBillingData) return null;
  if (selectedBilling)
    return (
      <BillingDetails
        data={billingDetailsData}
        onBack={onBack}
        isLoading={isLoadingBillingDetails}
      />
    );
  return (
    <BilingList
      data={billingData}
      isLoading={isLoadingBillingData}
      isFetching={isFetchingBillingData}
      onSelect={onSelectBilling}
    />
  );
};

export default BillingModal;
