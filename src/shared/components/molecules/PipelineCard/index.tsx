import Flex from '@shared/uikit/Flex';
import type { FC } from 'react';
import type { JobAPIProps } from '@shared/types/jobsProps';
import BaseBusinessJobCard from '../BusinessJobCard/BaseBusinessJobCard';
import type { CardWrapperProps } from '../CardItem/CardWrapper';
import StageSummary from './partials/StageSummary';

export interface PipelineCardProps {
  job: JobAPIProps;
  cardProps?: CardWrapperProps;
  moreOptions?: React.ReactNode;
}

const PipelineCard: FC<PipelineCardProps> = (props) => {
  const { job, cardProps, moreOptions } = props;
  return (
    <BaseBusinessJobCard
      cardProps={cardProps}
      id={job.id}
      title={job.title}
      image={job.pageCroppedImageUrl}
      username={job.pageTitle}
      location={job.location ?? ''}
      category={job.category}
      status={job.status}
      priority={job.priority}
      collaboratorsCount={job.collaborators?.length}
      applicantsCount={Number(job.applicantsCount)}
      candidatesCount={Number(job.candidatesCount)}
      lastUpdate={job.lastModifiedDate}
      showCounterDate
      inList
      actions={
        <Flex className="w-full !flex-row rounded overflow-hidden">
          {job.pipelines?.map((stage) => (
            <StageSummary
              key={`job_${job.id}_stage_${stage.id}`}
              count={stage.count}
              color={stage.color ?? 'brand'}
              title={stage.title}
            />
          ))}
        </Flex>
      }
      moreOptions={moreOptions}
    />
  );
};

export default PipelineCard;
