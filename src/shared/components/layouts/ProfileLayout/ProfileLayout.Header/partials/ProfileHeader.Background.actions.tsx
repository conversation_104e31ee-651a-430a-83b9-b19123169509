import React from 'react';
import Flex from 'shared/uikit/Flex';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useProfile } from '@shared/contexts/profile.provider';
import useProfilePage from 'shared/hooks/useProfilePage';
import {
  deleteHeaderBackground,
  deletePageHeaderBackground,
  setHeaderBackground,
  setPageHeaderBackground,
} from 'shared/utils/api/header';
import { useGetAboutSectionsData } from 'shared/hooks/api-hook';
import useUpdateQueryData from '@shared/utils/hooks/useUpdateQueryData';
import QueryKeys from '@shared/utils/constants/queryKeys';
import HeaderImageActions from './ProfileHeader.ImageActions';
import classes from './ProfileHeader.Background.actions.module.scss';

interface HeaderBackgroundActionsProps {
  imgSrc?: string;
  originalImg?: string;
  croppedImageData: {
    rotate?: number;
    zoom?: number;
    position?: { x: number; y: number };
  };
  popperMenuRef?: React.RefObject<HTMLElement>;
}

const ProfileHeaderBackgroundActions: React.FC<
  HeaderBackgroundActionsProps
> = ({ imgSrc, originalImg, croppedImageData, popperMenuRef }) => {
  const { t } = useTranslation();
  const { reFetchAboutSectionQuery } = useProfile();
  const { refetch: reFetchAuthUserQuery } = useGetAboutSectionsData();
  const { removeQueries: removeMyPagesQueries } = useUpdateQueryData([
    QueryKeys.myPages,
  ]);

  const { isPage, objectDetail, refetchObjectDetail } = useProfilePage();
  const pageId = objectDetail?.id;

  const { mutate: setUserAvatarHandler } = useReactMutation({
    apiFunc: setHeaderBackground,
  });

  const { mutate: setPageBackgroundHandler } = useReactMutation({
    apiFunc: setPageHeaderBackground,
  });

  const onSuccess = (
    { originalResponse, croppedResponse }: any,
    variables: any
  ) => {
    const croppedHeaderImageData = JSON.stringify(
      variables?.image?.croppedData || {}
    );

    if (isPage) {
      setPageBackgroundHandler(
        {
          pageId,
          headerImageUrl: originalResponse?.data?.link,
          croppedHeaderImageUrl: croppedResponse?.data?.link,
          croppedHeaderImageData,
        },
        {
          onSuccess: () => {
            refetchObjectDetail();
            removeMyPagesQueries();
          },
        }
      );
    } else {
      setUserAvatarHandler(
        {
          headerImageLink: originalResponse?.data?.link,
          croppedHeaderImageLink: croppedResponse?.data?.link,
          croppedHeaderImageData,
        },
        {
          onSuccess: () => {
            reFetchAboutSectionQuery();
            refetchObjectDetail();
          },
        }
      );
    }
  };

  const onSuccessDelete = () => {
    refetchObjectDetail();
    reFetchAboutSectionQuery();
    reFetchAuthUserQuery();
    if (isPage) {
      removeMyPagesQueries();
    }
  };

  return (
    <Flex className={classes.coverHover}>
      <Flex className={classes.imageIconContainer}>
        <HeaderImageActions
          popperMenuRef={popperMenuRef}
          imgSrc={imgSrc}
          originalImg={originalImg}
          onSuccess={onSuccess}
          onSuccessDelete={onSuccessDelete}
          deleteApi={
            isPage
              ? () => deletePageHeaderBackground(pageId)
              : deleteHeaderBackground
          }
          placement="bottom-end"
          className={classes.imageIcon}
          cropShape="rect"
          cropSize={{
            width: 980,
            height: 212,
          }}
          type="background"
          croppedImageData={croppedImageData}
          trySizeRatio={1}
          labels={{
            deleteHint: t('delete_cover_image_hint'),
            save: t('save'),
            view: t('view_cover_image'),
            edit: t('edit_cover_image'),
            delete: t('delete_cover_image'),
            upload: t('upload_another'),
            deleteBtn: t('delete'),
            editImage: t('edit_cover_img'),
            viewImage: t('view_cover_image'),
            deleteImage: t('delete_cover_image'),
          }}
        />
      </Flex>
    </Flex>
  );
};

export default ProfileHeaderBackgroundActions;
