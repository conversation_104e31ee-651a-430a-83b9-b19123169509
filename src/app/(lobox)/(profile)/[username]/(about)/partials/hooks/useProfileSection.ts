import type { ProfileSectionsStepKeys } from '@shared/components/Organism/MultiStepForm/ProfileSections/constants';
import type { ModalKeys } from '@shared/contexts/profileModals.provider';
import { useProfileModals } from '@shared/contexts/profileModals.provider';
import { useCallback, useMemo } from 'react';
import { openMultiStepForm } from 'shared/hooks/useMultiStepForm';

export const useProfileSection = (
  modalKey: ModalKeys,
  stepKey: ProfileSectionsStepKeys
) => {
  const { setSelectedForEdit, modals } = useProfileModals();
  const isEditMode = modals.selectedForEdit === modalKey;
  const handleBackClick = useCallback(
    () => setSelectedForEdit(undefined),
    [setSelectedForEdit]
  );

  const onEditHandler = useCallback(
    () =>
      openMultiStepForm({
        formName: 'editProfileSections',
        stepKey,
        data: {
          activeState: 'list',
          isSingle: true,
        },
      }),
    [stepKey]
  );

  const onClickHandler = useCallback(
    () =>
      openMultiStepForm({
        formName: 'editProfileSections',
        stepKey,
        data: {
          activeState: 'add',
          isSingle: true,
        },
      }),
    [stepKey]
  );

  return useMemo(
    () => ({
      isEditMode,
      handleBackClick,
      onEditHandler,
      onClickHandler,
    }),
    [isEditMode, handleBackClick, onEditHandler, onClickHandler]
  );
};
