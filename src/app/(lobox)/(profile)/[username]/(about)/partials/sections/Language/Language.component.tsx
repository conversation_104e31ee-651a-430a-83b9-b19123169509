import React from 'react';
import ProgressItem from 'shared/uikit/ProgressItem';
import IconButton from 'shared/uikit/Button/IconButton';
import PopperItem from 'shared/uikit/PopperItem';
import PopperMenu from 'shared/uikit/PopperMenu';
import useOpenConfirm from 'shared/uikit/Confirmation/useOpenConfirm';
import type { Language } from 'shared/types/language';
import { deleteLanguage } from 'shared/utils/api/geo';
import languageNormalizer from 'shared/utils/normalizers/languageNormalizer';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useGetAboutSectionsData } from 'shared/hooks/api-hook';
import { LANGUAGE_UPSERT } from 'shared/constants/profileModalsKeys';
import useProfilePage from 'shared/hooks/useProfilePage';
import partial from 'lodash/partial';
import { profileSectionsStepKeys } from 'shared/components/Organism/MultiStepForm/ProfileSections/constants';
import { openMultiStepForm } from 'shared/hooks/useMultiStepForm';
import Flex from '@shared/uikit/Flex';
import { useProfile } from '@shared/contexts/profile.provider';
import AboutSectionLayout from '@shared/components/Organism/AboutSectionLayout/AboutSectionLayout.component';
import { useProfileSection } from '../../hooks/useProfileSection';

interface Props {
  className: string;
}

const LanguageSection: React.FC<Props> = ({ className }) => {
  const { t } = useTranslation();
  const { isAuthUser } = useProfilePage();
  const { reFetchAboutSectionQuery } = useProfile();
  const { openConfirmDialog } = useOpenConfirm();
  const { data, isLoading } = useGetAboutSectionsData();
  const { handleBackClick, onEditHandler, isEditMode } = useProfileSection(
    LANGUAGE_UPSERT,
    profileSectionsStepKeys.LANGUAGE
  );

  const languages = data?.languages
    ?.map(languageNormalizer)
    ?.sort((a, b) => (a.progress < b.progress ? 1 : -1));

  const editHandler = (language: Language) => {
    openMultiStepForm({
      formName: 'editProfileSections',
      stepKey: profileSectionsStepKeys.LANGUAGE,
    });
  };

  const { mutate } = useReactMutation({
    apiFunc: deleteLanguage,
  });

  const openConfirm = (item: Language) => {
    openConfirmDialog({
      title: t('delete'),
      message: t('delete_language_helper'),
      confirmButtonText: t('delete'),
      cancelButtonText: t('cancel'),
      isAjaxCall: true,
      apiProps: {
        func: mutate,
        variables: item,
        onSuccess: reFetchAboutSectionQuery,
      },
    });
  };

  if (!languages?.length) {
    return null;
  }

  return (
    <Flex className={className}>
      <AboutSectionLayout
        isLoading={isLoading}
        title={t('languages')}
        data={languages}
        handleBackClick={isEditMode ? handleBackClick : undefined}
        onEditHandler={isAuthUser ? onEditHandler : undefined}
      >
        {({ data }) =>
          data?.map((item, index) => (
            <ProgressItem
              key={item?.id}
              image={item.image}
              title={item?.name}
              progressValue={item?.progress}
              progressSteps={7}
              tooltipText={t(item?.level)}
              styles={
                index !== 0 ? { root: 'responsive-margin-top' } : undefined
              }
              actionButton={
                isAuthUser ? (
                  <PopperMenu
                    placement="bottom-end"
                    buttonComponent={
                      <IconButton
                        type="far"
                        name="ellipsis-h"
                        size="sm"
                        colorSchema="transparent"
                      />
                    }
                  >
                    <PopperItem
                      onClick={partial(editHandler, item)}
                      iconName="pen"
                      label={t('edit_language')}
                    />
                    <PopperItem
                      onClick={partial(openConfirm, item)}
                      iconName="trash"
                      label={t('delete_language')}
                    />
                  </PopperMenu>
                ) : undefined
              }
            />
          ))
        }
      </AboutSectionLayout>
    </Flex>
  );
};

export default LanguageSection;
