import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useFormikContext } from 'formik';
import { use, useMemo } from 'react';
import type {
  BulkActionType,
  BulkItemProps,
  BulkPipelineFormProps,
  PipelineInfo,
} from '@shared/types/pipelineProps';
import Info from '@shared/components/molecules/Info/Info';
import Button from '@shared/uikit/Button';
import fileApi from 'shared/utils/api/file';
import { QueryKeys, schedulesDb } from '@shared/utils/constants/enums';
import { getCollaborators } from '@shared/utils/api/jobs';
import { useQuery } from '@tanstack/react-query';
import type { JobCollaboratorsProps } from '@shared/types/jobsProps';
import { searchAllTemplates } from '@shared/utils/api/template';
import { usePipelinePageCtx } from '../../../../PipelinePageProvider';

const commonProps = {
  normalizer: (data: any) =>
    data?.content?.map((item: any) => ({
      value: item.id,
      label: item.title,
      ...item,
    })),
  apiFunc: searchAllTemplates,
  cp: 'asyncAutoComplete',
  visibleOptionalLabel: false,
  showEllipsis: true,
};

interface IBulkStageMenuBodyProps {
  stage: PipelineInfo;
  selectedCounts: number;
}

const BulkStageMenuBody: React.FC<IBulkStageMenuBodyProps> = ({
  stage,
  selectedCounts,
}) => {
  const { t } = useTranslation();
  const { pipelinesData } = usePipelinePageCtx();

  const { values, setFieldValue, setErrors, resetForm } =
    useFormikContext<BulkPipelineFormProps>();
  const { data: collaborators } = useQuery<JobCollaboratorsProps>({
    queryKey: [QueryKeys.jobCollaborators, pipelinesData.job.id],
    queryFn: (props) => getCollaborators(props.queryKey[1] as string),
  });

  const groups = useMemo(() => {
    const list: any[] = [];
    const type = values.action?.value;
    if (type !== 'todo_2') {
      list.push({
        name: 'action',
        cp: 'dropdownSelect',
        label: t('action'),
        required: true,
        options: menuItems.map((item) => ({ ...item, label: t(item.label) })),
        disabled: !selectedCounts,
        onChange: (value: string) => {
          setErrors({});
          resetForm();
          setFieldValue('action', value);
        },
      });
    }
    if (type === 'move_to') {
      list.push({
        name: 'move_to',
        cp: 'dropdownSelect',
        label: t('move_to'),
        visibleOptionalLabel: false,
        options: pipelinesData.pipelines.reduce((acc, curr) => {
          if (curr.title === stage.title) return acc;
          return [
            ...acc,
            {
              value: curr.id as string,
              label: curr.title as string,
            },
          ];
        }, [] as BulkItemProps<string>[]),
      });
    }
    if (type === 'reject') {
      list.push({
        ...commonProps,
        name: 'reject_template',
        label: t('template'),
        params: {
          category: 'rejection',
        },
      });
    }
    if (type === 'interview') {
      list.push(
        {
          ...commonProps,
          name: 'interview_availability',
          label: t('availability'),
          showEllipsis: true,
          params: {
            category: 'meeting',
          },
        },
        {
          ...commonProps,
          name: 'interview_template',
          label: t('template'),
          params: {
            category: 'meeting',
          },
        }
      );
    }
    if (type === 'email') {
      list.push({
        ...commonProps,
        name: 'email_template',
        label: t('email'),
        params: {
          category: 'email',
        },
      });
    }
    if (type === 'message') {
      list.push({
        ...commonProps,
        name: 'message_template',
        label: t('email'),
        params: {
          category: 'email',
        },
      });
    }
    if (type === 'note') {
      setFieldValue('note_visibility', 'TEAM');
      list.push(
        {
          formGroup: {
            color: 'border',
            title: t('visibility'),
            className: '!p-0 !m-0 !mb-8',
            titleProps: {
              font: '400',
              size: 15,
            },
          },
          name: 'note_visibility',
          cp: 'radioGroup',
          visibleOptionalLabel: false,
          options: [
            { value: 'TEAM', label: t('team') },
            { value: 'ONLY_ME', label: t('only_me') },
          ],
          classNames: {
            root: '-ml-8',
            itemWrapper: '!mb-4',
          },
        },
        {
          name: 'note_text',
          cp: 'richtext',
          visibleOptionalLabel: false,
          showEmoji: false,
          label: t('write_note'),
        },
        {
          name: 'note_attachment',
          cp: 'attachmentPicker',
          label: t('attachment'),
          visibleOptionalLabel: false,
          uploadApi: fileApi.uploadFile,
        }
      );
    }
    if (type === 'todo_1') {
      list.push(
        {
          name: 'todo_assignee',
          cp: 'avatarAsyncAutoComplete',
          label: t('assignee'),
          visibleOptionalLabel: false,
          required: true,
          options: [
            ...(collaborators?.collaborators || []),
            ...(collaborators?.pointOfContact
              ? [collaborators?.pointOfContact]
              : []),
          ].map((user) => ({
            value: user.id,
            label: user.fullName,
            image: user.croppedImageUrl,
            helperText: user.username,
          })),
          disabled:
            !collaborators?.collaborators.length &&
            !collaborators?.pointOfContact,
          editable: false,
          onSelect: (item: any) => {
            setFieldValue('todo_assignee', item);
          },
          visibleRightIcon: true,
        },
        {
          name: 'todo_title',
          cp: 'input',
          visibleOptionalLabel: false,
          label: t('title'),
        },
        {
          name: 'todo_text',
          cp: 'richtext',
          visibleOptionalLabel: false,
          showEmoji: false,
          label: t('description'),
        },
        {
          name: 'todo_attachment',
          cp: 'attachmentPicker',
          label: t('attachment'),
          visibleOptionalLabel: false,
          uploadApi: fileApi.uploadFile,
        }
      );
    }
    if (type === 'todo_2') {
      const startTime = values.todo_start_time?.value;
      const endTime = values.todo_end_time?.value;
      const filteredStartTimeOptions = schedulesDb.timeOptions.filter(
        (option) => {
          if (!endTime) return true;
          return option.value < endTime;
        }
      );
      const filteredEndTimeOptions = schedulesDb.timeOptions.filter(
        (option) => {
          if (!startTime) return true;
          return option.value > startTime;
        }
      );

      list.push(
        {
          name: 'todo_status',
          cp: 'cardBadge',
          options: [
            { label: t('OPEN'), value: 'OPEN' },
            { label: t('ON_HOLD'), value: 'ON_HOLD' },
            { label: t('DONE'), value: 'DONE' },
          ],
          value: values.todo_status ?? {
            label: t('open'),
            value: 'OPEN',
          },
        },
        {
          name: 'todo_start_date',
          cp: 'datePicker',
          variant: 'input',
          picker: 'date',
          minDate: new Date(),
          label: t('start_date'),
        },
        {
          name: 'todo_start_time',
          cp: 'dropdownSelect',
          label: t('start_time'),
          options: filteredStartTimeOptions,
          onChange: (value: any) => {
            // Clear end time if it becomes invalid
            if (endTime && value.value >= endTime) {
              setFieldValue('todo_end_time', null);
            }
          },
          rightIconProps: { name: 'clock' },
        },
        {
          name: 'todo_end_date',
          cp: 'datePicker',
          variant: 'input',
          picker: 'date',
          minDate: values.todo_start_date
            ? new Date(values.todo_start_date)
            : new Date(),
          label: t('end_date'),
        },
        {
          name: 'todo_end_time',
          cp: 'dropdownSelect',
          label: t('end_time'),
          options: filteredEndTimeOptions,
          onChange: (value: any) => {
            if (startTime && value.value <= startTime) {
              setFieldValue('todo_start_time', null);
            }
          },
          rightIconProps: { name: 'clock' },
        }
      );
    }
    return list;
  }, [
    values,
    pipelinesData,
    stage,
    selectedCounts,
    setErrors,
    setFieldValue,
    resetForm,
  ]);

  return (
    <>
      <DynamicFormBuilder groups={groups} className="gap-10" />
      {!!values.action &&
        values.action?.value !== 'move_to' &&
        values.action?.value !== 'note' &&
        !values.action?.value.includes('todo') && (
          <Info
            text={t('bulk_actions_require_templates')}
            color="secondaryDisabledText"
            textColor="secondaryDisabledText"
            className="flex-wrap mt-10"
            otherComponents={
              <Button
                fullWidth
                label={t('modify_template')}
                schema="semi-transparent"
                className="mt-8"
                onClick={() => alert('Todo: needs to be implemented later!')}
              />
            }
          />
        )}
    </>
  );
};

export default BulkStageMenuBody;

const menuItems: BulkItemProps<BulkActionType>[] = [
  {
    value: 'move_to',
    label: 'move_to',
  },
  {
    value: 'reject',
    label: 'reject',
  },
  {
    value: 'interview',
    label: 'interview',
  },
  {
    value: 'email',
    label: 'email',
  },
  {
    value: 'message',
    label: 'message',
  },
  {
    value: 'note',
    label: 'note',
  },
  {
    value: 'todo_1',
    label: 'to_do',
  },
];
