import Flex from '@shared/uikit/Flex';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import type {
  BulkPipelineFormProps,
  PipelineInfo,
} from '@shared/types/pipelineProps';
import Form from '@shared/uikit/Form';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  batchAddNoteToCandidates,
  batchAddTodoToCandidates,
  batchMoveCandidatesToStage,
  pipelineBatchReject,
} from '@shared/utils/api/jobs';
import useToast from '@shared/uikit/Toast/useToast';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { QueryKeys } from '@shared/utils/constants';
import { use, useRef } from 'react';
import type { PipelineStageFilter } from '@shared/types/jobsProps';
import moment from 'moment';
import BulkStageMenuBody from './BulkStageMenu/BulkStageMenuBody';
import BulkStageMenuFooter from './BulkStageMenu/BulkStageMenuFooter';
import {
  usePipelinePageCtx,
  validateBulkMenu,
} from '../../../PipelinePageProvider';

interface BulkStageMenuProps {
  selectedCandidates: { id: string }[];
  stage: PipelineInfo;
  onCloseBulk: VoidFunction;
  wrapperHeight?: number;
  innerFilter: PipelineStageFilter;
}

const BulkStageMenu: React.FC<BulkStageMenuProps> = (props) => {
  const {
    selectedCandidates,
    stage,
    onCloseBulk,
    innerFilter,
    wrapperHeight = 0,
  } = props;
  const toast = useToast();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { filter } = usePipelinePageCtx();
  const targetPipelineId = useRef<string>();

  const onSuccess = () => {
    queryClient.invalidateQueries({
      queryKey: [
        QueryKeys.getPipelineParticipants,
        String(stage.id),
        filter,
        innerFilter,
      ],
      exact: false,
    });
    if (targetPipelineId.current) {
      queryClient.invalidateQueries({
        queryKey: [
          QueryKeys.getPipelineParticipants,
          String(targetPipelineId.current),
          filter,
          innerFilter,
        ],
        exact: false,
      });
      targetPipelineId.current = undefined;
    }
    onCloseBulk();
  };
  const onError = (error: any) => {
    let message = t('error_moving_candidates');
    if (error?.response?.data?.message) {
      message = error.response.data.message;
    }
    onAlert(t('error'), message, 'error');
  };

  const onAlert = (
    title: string,
    message: string,
    type: 'success' | 'error'
  ) => {
    toast({
      type,
      icon: `${type === 'success' ? 'check' : 'times'}-circle`,
      title,
      message,
    });
  };
  const { mutate: moveCandidatesToStage } = useMutation({
    mutationFn: batchMoveCandidatesToStage,
    onSuccess: () => {
      onAlert(t('candidate_moved'), t('selected_candidates_moved'), 'success');
      onSuccess();
    },
    onError,
  });
  const { mutate: batchRejectHandler } = useMutation({
    mutationFn: pipelineBatchReject,
    onSuccess: () => {
      onAlert(
        t('candidate_rejected'),
        t('selected_candidates_rejected'),
        'success'
      );
      onSuccess();
    },
    onError,
  });
  const { mutate: addNote } = useMutation({
    mutationFn: batchAddNoteToCandidates,
    onSuccess: () => {
      onAlert(
        t('note_added'),
        t('note_added_to_selected_candidates'),
        'success'
      );
      onSuccess();
    },
    onError,
  });
  const { mutate: addTodo } = useMutation({
    mutationFn: batchAddTodoToCandidates,
    onSuccess: () => {
      onAlert(
        t('todo_added'),
        t('todo_added_to_selected_candidates'),
        'success'
      );
      onSuccess();
    },
  });
  const apiFunc = (data: BulkPipelineFormProps) => {
    const participationIds = selectedCandidates.map(
      (candidate) => candidate.id
    );
    switch (data.action?.value) {
      case 'move_to': {
        targetPipelineId.current = data.move_to?.value;
        moveCandidatesToStage({
          pipelineId: data.move_to?.value ?? '',
          participationIds,
        });
        break;
      }
      case 'reject': {
        batchRejectHandler({ participationIds });
        break;
      }
      case 'interview': {
        alert('TODO: need to implement interview');
        break;
      }
      case 'email': {
        alert('TODO: need to implement email');
        break;
      }
      case 'message': {
        alert('TODO: need to implement message');
        break;
      }
      case 'note': {
        addNote({
          participationIds,
          body: data.note_text ?? '',
          fileIds: data.note_attachment?.map((file) => file.id) ?? [],
          visibility: data.note_visibility ?? 'ONLY_ME',
        });
        break;
      }
      case 'todo_2': {
        const start = data.todo_start_date
          ? moment(data.todo_start_date)
              .set({
                hour: parseInt(
                  data.todo_start_time?.value.split(':')[0] ?? '0',
                  10
                ),
                minute: parseInt(
                  data.todo_start_time?.value.split(':')[1] ?? '0',
                  10
                ),
                second: 0,
              })
              .toISOString()
          : undefined;
        const end = data.todo_end_date
          ? moment(data.todo_end_date)
              .set({
                hour: parseInt(
                  data.todo_end_time?.value.split(':')[0] ?? '0',
                  10
                ),
                minute: parseInt(
                  data.todo_end_time?.value.split(':')[1] ?? '0',
                  10
                ),
                second: 0,
              })
              .toISOString()
          : undefined;

        addTodo({
          participationIds,
          assigneeUserId: data.todo_assignee?.value ?? 0,
          title: data.todo_title ?? '',
          description: data.todo_text ?? '',
          status: data.todo_status?.value ?? 'OPEN',
          start,
          end,
          fileIds: data.todo_attachment?.map((file) => file.id) ?? [],
        });
        break;
      }
      default:
        break;
    }
  };

  return (
    <Flex className="sticky bottom-0 left-0 w-full">
      <Form
        initialValues={{}}
        validate={validateBulkMenu}
        apiFunc={apiFunc as (data: unknown) => Promise<any>}
        className="flex flex-col items-end"
      >
        {({ values, isSubmitting }) => (
          <Flex
            style={{ maxHeight: `${wrapperHeight - 57}px` }}
            className="w-full"
          >
            <ModalBody className="!bg-popOverBg_white sticky bottom-[101px] overflow-y-auto  pt-[57px]">
              <BulkStageMenuBody
                stage={stage}
                selectedCounts={selectedCandidates.length}
              />
            </ModalBody>
            <ModalFooter className="gap-12 w-full">
              <BulkStageMenuFooter
                selectedCounts={selectedCandidates.length}
                onDiscard={onCloseBulk}
                isSubmitting={isSubmitting}
              />
            </ModalFooter>
            {!!(values as BulkPipelineFormProps)?.action && (
              <Flex
                className="absolute w-full h-full bottom-0 left-0 bg-modalBlur"
                style={{ height: `${wrapperHeight}px` }}
              />
            )}
          </Flex>
        )}
      </Form>
    </Flex>
  );
};

export default BulkStageMenu;
