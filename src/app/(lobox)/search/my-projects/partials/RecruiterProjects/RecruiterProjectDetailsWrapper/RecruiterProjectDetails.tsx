import type { RecruiterProjectDetailsTabsProps } from '@shared/types/projectsProps';
import Flex from '@shared/uikit/Flex';
import { useState, type FC } from 'react';
import dynamic from 'next/dynamic';
import type { ProjectProps } from '@shared/types/project';
import RecruiterProjectDetailsCard from './RecruiterProjectDetails/RecruiterProjectDetailsCard';

const ProjectFullDetails = dynamic(
  () => import('./RecruiterProjectDetails/ProjectFullDetails')
);
const RecruiterProjectDetailsJobs = dynamic(
  () => import('./RecruiterProjectDetails/RecruiterProjectDetailsJobs')
);
const RecruiterProjectDetailsTodos = dynamic(
  () => import('./RecruiterProjectDetails/RecruiterProjectDetailsTodos')
);
const RecruiterProjectDetailsInsights = dynamic(
  () => import('./RecruiterProjectDetails/RecruiterProjectDetailsInsights')
);
const RecruiterProjectDetailsMeetings = dynamic(
  () => import('./RecruiterProjectDetails/RecruiterProjectDetailsMeetings')
);
const RecruiterProjectDetailsActivities = dynamic(
  () => import('./RecruiterProjectDetails/RecruiterProjectDetailsActivities')
);
const RecruiterProjectDetailsApplicants = dynamic(
  () => import('./RecruiterProjectDetails/RecruiterProjectDetailsApplicants')
);
const RecruiterProjectDetailsCandidates = dynamic(
  () => import('./RecruiterProjectDetails/RecruiterProjectDetailsCandidates')
);
const RecruiterProjectDetailsAssignees = dynamic(
  () => import('./RecruiterProjectDetails/RecruiterProjectDetailsAssignees')
);

interface RecruiterProjectDetailsProps {
  project: ProjectProps;
}

const RecruiterProjectDetails: FC<RecruiterProjectDetailsProps> = ({
  project,
}) => {
  const [selectedTab, setSelectedTab] =
    useState<RecruiterProjectDetailsTabsProps>('details');
  return (
    <Flex className="flex-1">
      <RecruiterProjectDetailsCard
        project={project}
        onChangeTab={(tab) =>
          setSelectedTab(tab as RecruiterProjectDetailsTabsProps)
        }
        selectedTab={selectedTab}
      />
      <Panels project={project} tab={selectedTab} />
    </Flex>
  );
};

export default RecruiterProjectDetails;

const Panels = ({
  tab,
  project,
}: {
  tab: RecruiterProjectDetailsTabsProps;
  project: ProjectProps;
}) => {
  switch (tab) {
    case 'jobs': {
      return <RecruiterProjectDetailsJobs projectId={project.id} />;
    }
    case 'applicants': {
      return <RecruiterProjectDetailsApplicants project={project} />;
    }
    case 'candidates': {
      return <RecruiterProjectDetailsCandidates projectId={project.id} />;
    }
    case 'assignees': {
      return (
        <RecruiterProjectDetailsAssignees
          assignees={project.collaboratorUsers}
        />
      );
    }
    case 'todos': {
      return <RecruiterProjectDetailsTodos projectId={project.id} />;
    }
    case 'meetings': {
      return <RecruiterProjectDetailsMeetings projectId={project.id} />;
    }
    case 'activities': {
      return <RecruiterProjectDetailsActivities projectId={project.id} />;
    }
    case 'insights': {
      return <RecruiterProjectDetailsInsights />;
    }
    default: {
      return <ProjectFullDetails project={project} />;
    }
  }
};
