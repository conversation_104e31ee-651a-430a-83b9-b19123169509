import SendMessageButton from '@shared/components/molecules/SendMessageButton';
import type { CandidateFormData } from '@shared/types/candidates';
import Button from '@shared/uikit/Button';
import { getCandidateById } from '@shared/utils/api/candidates';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import dynamic from 'next/dynamic';
import { useCallback, useEffect, useState, type FC } from 'react';
import Tabs from 'shared/components/Organism/Tabs';
import CandidateCard from 'shared/components/molecules/CandidateCard/CandidateCard';
import Flex from 'shared/uikit/Flex';
import { QueryKeys } from 'shared/utils/constants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import CardWrapper from '@shared/components/molecules/CardItem/CardWrapper';
import ComingSoon from '@shared/svg/ComingSoon';
import type { CandidateManagerTabkeys } from '@shared/components/Organism/CandidateManager/tabs';
import classes from './CandidateDetails.module.scss';
import CandidateDetailsSkeleton from './CandidateDetails.skeleton';
import type { BaseCandidateSectionProp } from './types';
import CandidateAboutSkeleton from './tab1.CandidateAbout/CandidateAbout.skeleton';
import SimilarCandidatesSkeleton from './tab4.CandidateSimilar/CandidateSimilar.skeleton';
import CandidateActivitesSkeleton from './tab5.CandidateActivites/CandidateActivites.skeleton';
import CandidateJobsSkeleton from './tab2.CandidateJobs/CandidateJobs.skeleton';

const CandidateAboutTab = dynamic(() => import('./tab1.CandidateAbout'), {
  loading: () => <CandidateAboutSkeleton />,
});

const CandidateJobsTab = dynamic(() => import('./tab2.CandidateJobs'), {
  ssr: false,
  loading: () => <CandidateJobsSkeleton />,
});

const CandidateSimilarTab = dynamic(() => import('./tab4.CandidateSimilar'), {
  loading: () => <SimilarCandidatesSkeleton />,
});
const CandidateActivitesTab = dynamic(
  () => import('./tab5.CandidateActivites'),
  {
    loading: () => <CandidateActivitesSkeleton />,
  }
);
const CandidateInsightsTab = dynamic(() => import('./tab6.CandidateInsights'));

type CandidateDetailsTabsNames =
  | 'about'
  | 'jobs'
  | 'availability'
  | 'similar'
  | 'activities'
  | 'insights';

interface CandidateDetailsProps {
  candidateId?: string | null;
  parentLoading?: boolean;
}

const tabs: Array<{ path: CandidateDetailsTabsNames; title: string }> = [
  {
    path: 'about',
    title: 'about',
  },
  {
    path: 'jobs',
    title: 'jobs',
  },
  {
    path: 'availability',
    title: 'availability',
  },
  {
    path: 'similar',
    title: 'similar',
  },
  {
    path: 'activities',
    title: 'activities',
  },
  {
    path: 'insights',
    title: 'insights',
  },
];

const CandidateDetails: FC<CandidateDetailsProps> = ({
  candidateId,
  parentLoading,
}) => {
  const { t } = useTranslation();

  const appDispatch = useGlobalDispatch();
  const [selectedTab, setSelectedTab] =
    useState<CandidateDetailsTabsNames>('about');

  useEffect(() => {
    setSelectedTab('about');
  }, [candidateId]);

  const { data: candidate, isLoading } = useReactQuery<CandidateFormData>({
    action: {
      apiFunc: () => getCandidateById(candidateId!),
      key: [QueryKeys.getCandidate, candidateId],
    },
    config: {
      enabled: !!candidateId,
    },
  });

  const handleOpenManager = useCallback(
    (tab?: CandidateManagerTabkeys) => {
      if (candidate)
        appDispatch({
          type: 'TOGGLE_CANDIDATE_MANAGER',
          payload: {
            isOpen: true,
            tab,
            id: candidate.id,
            enableNavigate: true,
          },
        });
    },
    [appDispatch, candidate]
  );

  if (isLoading || parentLoading) return <CandidateDetailsSkeleton />;

  if (!candidate) return null;

  return (
    <>
      <CandidateCard
        candidate={candidate}
        showActions
        showBadges
        showTags
        showIsManual
        onBadgeClick={handleOpenManager}
        footer={
          <Tabs
            activePath={selectedTab}
            onChangeTab={setSelectedTab}
            styles={{
              tabsRoot: classes.tabsRoot,
              linksRoot: classes.linksRoot,
            }}
            tabs={tabs.map((tab) => ({ ...tab, title: t(tab.title) }))}
          />
        }
      >
        <Flex className="!flex-row gap-12">
          {candidate.profile?.username ? (
            <SendMessageButton
              className="flex-1"
              disabled={!candidate.profile?.username}
              object={{
                id: candidate.profile.originalId,
                croppedImageUrl: candidate.profile.croppedImageUrl,
                fullName: candidate.profile.fullName,
                username: candidate.profile.username,
                isPage: false,
              }}
              fullWidth
            />
          ) : (
            <Button
              disabled
              className="flex-1"
              schema="semi-transparent"
              leftIcon="envelope"
              leftType="far"
              label={t('message')}
              fullWidth
            />
          )}
          <Button
            className="flex-1"
            label={t('manage')}
            leftIcon="user-cog"
            fullWidth
            onClick={handleOpenManager}
          />
        </Flex>
      </CandidateCard>
      <Panels candidate={candidate} tab={selectedTab} />
    </>
  );
};

export default CandidateDetails;

const Panels = ({
  tab,
  candidate,
}: BaseCandidateSectionProp & {
  tab: CandidateDetailsTabsNames;
}) => {
  switch (tab) {
    case 'jobs': {
      return <CandidateJobsTab candidate={candidate} />;
    }
    case 'availability': {
      return (
        <CardWrapper
          classNames={{
            root: '!mt-32 !flex-1',
            container: '!w-full !h-full py-32 !justify-center !items-center',
          }}
        >
          <ComingSoon />
        </CardWrapper>
      );
    }
    case 'similar': {
      return <CandidateSimilarTab candidate={candidate} />;
    }
    case 'activities': {
      return <CandidateActivitesTab candidate={candidate} />;
    }
    case 'insights': {
      return <CandidateInsightsTab candidate={candidate} />;
    }
    default: {
      return <CandidateAboutTab candidate={candidate} />;
    }
  }
};
